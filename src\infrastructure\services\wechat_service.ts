import { WECHAT_APP_ID, WECHAT_APP_SECRET } from "../../config.ts";

const CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";
const ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
const GET_PHONE_NUMBER_URL =
  "https://api.weixin.qq.com/wxa/business/getuserphonenumber";

interface Code2SessionResponse {
  openid: string;
  session_key: string;
  unionid?: string;
  errcode?: number;
  errmsg?: string;
}

interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  errcode?: number;
  errmsg?: string;
}

interface PhoneInfo {
  phoneNumber: string;
  purePhoneNumber: string;
  countryCode: number;
  watermark: {
    timestamp: number;
    appid: string;
  };
}

interface GetPhoneNumberResponse {
  errcode: number;
  errmsg: string;
  phone_info: PhoneInfo;
}

export class WeChatService {
  private accessToken: string | null = null;
  private accessTokenExpiresAt = 0;

  /**
   * @description 使用登录凭证 code 获取 session_key 和 openid/unionid
   * @param code 登录凭证
   * @returns Promise<Code2SessionResponse>
   */
  public async code2Session(code: string): Promise<Code2SessionResponse> {
    const params = new URLSearchParams({
      appid: WECHAT_APP_ID,
      secret: WECHAT_APP_SECRET,
      js_code: code,
      grant_type: "authorization_code",
    });

    const response = await fetch(`${CODE2SESSION_URL}?${params.toString()}`);
    const data: Code2SessionResponse = await response.json();

    if (data.errcode && data.errcode !== 0) {
      throw new Error(`微信 code2session 接口调用失败: [${data.errcode}] ${data.errmsg}`);
    }

    return data;
  }

  /**
   * @description 使用 code 获取用户手机号(新版接口)
   * @see https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html
   * @param code 手机号获取凭证
   * @returns Promise<PhoneInfo>
   */
  public async getPhoneNumber(code: string): Promise<PhoneInfo> {
    const accessToken = await this.getAccessToken();
    const url = `${GET_PHONE_NUMBER_URL}?access_token=${accessToken}`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ code }),
    });

    const data: GetPhoneNumberResponse = await response.json();

    if (data.errcode !== 0) {
      throw new Error(`获取微信手机号失败: [${data.errcode}] ${data.errmsg}`);
    }

    return data.phone_info;
  }

  /**
   * @description 解密微信加密数据（如手机号）
   * @param sessionKey 会话密钥
   * @param iv 初始化向量
   * @param encryptedData 加密数据
   * @returns Promise<any> 解密后的数据
   */
  public async decryptData(sessionKey: string, iv: string, encryptedData: string): Promise<any> {
    const sessionKeyBuffer = new Uint8Array(atob(sessionKey).split('').map(char => char.charCodeAt(0)));
    const ivBuffer = new Uint8Array(atob(iv).split('').map(char => char.charCodeAt(0)));
    const encryptedBuffer = new Uint8Array(atob(encryptedData).split('').map(char => char.charCodeAt(0)));
    
    const key = await crypto.subtle.importKey(
      "raw",
      sessionKeyBuffer,
      { name: "AES-CBC" },
      false,
      ["decrypt"]
    );

    const decrypted = await crypto.subtle.decrypt(
      { name: "AES-CBC", iv: ivBuffer },
      key,
      encryptedBuffer
    );
    
    const decoder = new TextDecoder();
    const jsonString = decoder.decode(decrypted);

    // 去除填充
    const unpadded = this.unpad(jsonString);
    return JSON.parse(unpadded);
  }

  private unpad(str: string): string {
    const pad = str.charCodeAt(str.length - 1);
    return str.slice(0, -pad);
  }

  /**
   * 获取并缓存微信 access_token
   */
  private async getAccessToken(): Promise<string> {
    if (this.accessToken && Date.now() < this.accessTokenExpiresAt) {
      return this.accessToken;
    }

    const params = new URLSearchParams({
      grant_type: "client_credential",
      appid: WECHAT_APP_ID,
      secret: WECHAT_APP_SECRET,
    });

    const response = await fetch(`${ACCESS_TOKEN_URL}?${params.toString()}`);
    const data: AccessTokenResponse = await response.json();

    if (!data.access_token || (data.errcode && data.errcode !== 0)) {
      throw new Error(
        `获取微信 access_token 失败: [${data.errcode}] ${data.errmsg}`
      );
    }

    this.accessToken = data.access_token;
    // expires_in 是秒，提前 5 分钟刷新
    this.accessTokenExpiresAt = Date.now() + (data.expires_in - 300) * 1000;

    return this.accessToken;
  }
} 