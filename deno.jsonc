{"tasks": {"dev": "deno run --allow-net --allow-read --allow-env --env=.env.development src/main.ts", "start": "deno run --allow-net --allow-read --allow-env --env=.env src/main.ts", "test": "deno test --allow-net --allow-read", "test:unit": "deno test --allow-net --allow-read tests/unit", "test:integration": "deno test --allow-net --allow-read tests/integration", "db:init": "psql -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-mydatabase} -a -f init_db.sql", "db:reset": "psql -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-mydatabase} -c 'DROP TABLE IF EXISTS users CASCADE;' && deno task db:init"}, "importMap": "./import_map.json", "compilerOptions": {"lib": ["deno.window"], "strict": true, "experimentalDecorators": true}, "lint": {"rules": {"tags": ["recommended"], "include": [], "exclude": []}}, "fmt": {"options": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "singleQuote": false}}}