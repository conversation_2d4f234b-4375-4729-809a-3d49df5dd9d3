import { Context, Next } from "oak";
import { BusinessError, BusinessCode } from "../../../domain/errors/business_error.ts";
import { z } from "@dklab/oak-routing-ctrl";

export async function jsonErrorHandler(ctx: Context, next: Next) {
  try {
    await next();
  } catch (error: unknown) {
    if (error instanceof z.ZodError) {
        ctx.response.body = {
            success: false,
            code: BusinessCode.VALIDATION_ERROR,
            message: "请求参数校验失败",
            details: error.errors,
        };
    } else if (error instanceof BusinessError) {
      ctx.response.body = {
        success: false,
        code: error.code,
        message: error.message,
      };
    } else if (error instanceof Error) {
      console.error("未处理的控制器错误:", error);
      ctx.response.body = {
        success: false,
        code: 50000,
        message: "服务器内部错误",
        error: error.message,
      };
    } else {
        // 处理非 Error 类型的抛出
        ctx.response.body = {
          success: false,
          code: 50000,
          message: "服务器发生未知类型的错误",
        };
    }
  }
} 