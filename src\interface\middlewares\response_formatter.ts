import { Context, isHttpError, Status } from "oak";
import { BusinessError, BusinessCode,  CodeMessage } from "../constants/error_codes.ts";

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  message: string | null;
  code?: BusinessCode | string; // 统一业务码或HTTP状态码的字符串表示
  error?: {
    code: BusinessCode | string; // 详细错误码
    message: string;
    details?: unknown;
  } | null;
}

export async function responseFormatter(ctx: Context, next: () => Promise<unknown>) {
  try {
    await next();

    // 如果响应体已经被其他中间件或路由处理函数填充，并且不是我们期望的 ApiResponse 结构
    // 则将其包装成标准的成功响应格式
    if (ctx.response.body && (typeof ctx.response.body !== 'object' || !('success' in ctx.response.body))) {
      const originalBody = ctx.response.body;
      const response: ApiResponse<unknown> = {
        success: true,
        data: originalBody,
        message: ctx.response.status === 204 ? CodeMessage[BusinessCode.SUCCESS] : (CodeMessage[BusinessCode.SUCCESS] || '操作成功'), // 默认为操作成功，或根据实际情况调整
        code: BusinessCode.SUCCESS, // 成功的业务码，可以定义一个专门的 SUCCESS_CODE
      };
      ctx.response.body = response;
    }
    // 如果没有响应体，且状态码是 2xx 但不是 204 No Content, 补充一个默认的成功响应
    else if (!ctx.response.body && ctx.response.status >= 200 && ctx.response.status < 300 && ctx.response.status !== 204) {
      ctx.response.body = {
        success: true,
        data: null,
        message: '操作成功',
        code: BusinessCode.SUCCESS, //  TODO: 定义一个 SUCCESS_CODE
      };
    }
    // 对于 204 No Content，确保不返回响应体
    else if (ctx.response.status === 204) {
      ctx.response.body = undefined;
    }

  } catch (err) {
    let response: ApiResponse<null>;

    if (err instanceof BusinessError) {
      ctx.response.status = err.httpStatus;
      response = {
        success: false,
        data: null,
        message: err.message,
        code: err.code,
        error: {
          code: err.code,
          message: err.message,
          details: err.details,
        },
      };
    } else if (isHttpError(err)) {
      ctx.response.status = err.status;
      const errorCode = Object.keys(Status).find(key => Status[key as keyof typeof Status] === err.status) || BusinessCode.UNKNOWN_ERROR;
      response = {
        success: false,
        data: null,
        message: err.message || CodeMessage[errorCode as BusinessCode] || '发生服务器错误。',
        code: errorCode as BusinessCode, // 使用HTTP状态码的文本表示作为错误码
        error: {
          code: errorCode as BusinessCode,
          message: err.message || CodeMessage[errorCode as BusinessCode] || '发生服务器错误。',
        },
      };
    } else {
      ctx.response.status = 500;
      response = {
        success: false,
        data: null,
        message: CodeMessage[BusinessCode.UNKNOWN_ERROR],
        code: BusinessCode.UNKNOWN_ERROR,
        error: {
          code: BusinessCode.UNKNOWN_ERROR,
          message: (err instanceof Error ? err.message : CodeMessage[BusinessCode.UNKNOWN_ERROR]),
          details: (err instanceof Error && err.stack) ? err.stack.split('\n') : undefined,
        },
      };
      console.error('Unhandled error:', err); // 记录未处理的错误
    }
    ctx.response.body = response;
  }
  ctx.response.type = "json";
}