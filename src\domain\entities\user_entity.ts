// src/domain/entities/user_entity.ts

import { PhoneNumberVO } from "../value_objects/phone_number_vo.ts";

/**
 * @description 用户实体接口
 */
export interface IUserEntity {
  id: string; // 通常使用 UUID
  phoneNumber: PhoneNumberVO;
  passwordHash?: string; // 存储密码的哈希值, 可选
  wechatUnionId?: string; // 微信统一ID，可选
  wechatOpenId?: string; // 微信开放ID，可选
  createdAt: Date;
  updatedAt: Date;
  // 可以添加更多用户属性，例如：nickname, avatarUrl, status 等
}

/**
 * @description 用户实体类
 */
export class UserEntity implements IUserEntity {
  public readonly id: string;
  public phoneNumber: PhoneNumberVO;
  public passwordHash?: string;
  public wechatUnionId?: string;
  public wechatOpenId?: string;
  public readonly createdAt: Date;
  public updatedAt: Date;

  protected constructor(
    id: string, 
    phoneNumber: PhoneNumberVO, 
    passwordHash?: string,
    wechatUnionId?: string,
    wechatOpenId?: string,
    createdAt?: Date, 
    updatedAt?: Date
  ) {
    this.id = id;
    this.phoneNumber = phoneNumber;
    this.passwordHash = passwordHash;
    this.wechatUnionId = wechatUnionId;
    this.wechatOpenId = wechatOpenId;
    this.createdAt = createdAt || new Date();
    this.updatedAt = updatedAt || new Date();
  }

  /**
   * @description 创建一个新的用户实体
   * @param id 用户ID (建议使用 UUID)
   * @param phoneNumber 手机号码值对象
   * @param passwordHash 密码哈希
   * @param wechatUnionId 微信统一ID
   * @param wechatOpenId 微信开放ID
   * @returns UserEntity 实例
   */
  public static create(id: string, phoneNumber: PhoneNumberVO, passwordHash?: string, wechatUnionId?: string, wechatOpenId?: string): UserEntity {
    // 在这里可以添加创建用户时的业务规则校验，例如ID是否已存在等（通常由仓储或领域服务处理）
    return new UserEntity(id, phoneNumber, passwordHash, wechatUnionId, wechatOpenId);
  }

  /**
   * @description 更新用户手机号码
   * @param newPhoneNumber 新的手机号码值对象
   */
  public changePhoneNumber(newPhoneNumber: PhoneNumberVO): void {
    // 可以在这里添加更新手机号时的业务规则，例如是否与旧手机号相同等
    this.phoneNumber = newPhoneNumber;
    this.updatedAt = new Date();
  }

  // 可以添加更多与用户相关的业务方法
}