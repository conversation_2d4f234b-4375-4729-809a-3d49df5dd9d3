import { create, verify, getNumericDate } from "djwt";
import { JWT_SECRET_KEY, JWT_EXPIRATION_SECONDS } from "../../config.ts";
import { UserAggregate } from "../../domain/aggregates/user_aggregate.ts";

export interface JwtPayload {
  userId: string;
  exp: number;
}

export class JwtService {
  private key: CryptoKey | null = null;

  private async ensureKey(): Promise<CryptoKey> {
    if (!this.key) {
      this.key = await crypto.subtle.importKey(
        "raw",
        new TextEncoder().encode(JWT_SECRET_KEY),
        { name: "HMAC", hash: "SHA-256" },
        false,
        ["sign", "verify"]
      );
    }
    return this.key;
  }

  public async createToken(user: UserAggregate): Promise<string> {
    const key = await this.ensureKey();
    const payload: JwtPayload = {
      userId: user.id,
      exp: getNumericDate(JWT_EXPIRATION_SECONDS),
    };

    return await create({ alg: "HS256", typ: "JWT" }, payload, key);
  }

  public async verifyToken(token: string): Promise<JwtPayload> {
    const key = await this.ensureKey();
    return await verify(token, key) as unknown as JwtPayload;
  }
} 