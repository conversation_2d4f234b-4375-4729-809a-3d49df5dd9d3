// src/domain/repositories/service_account_repository.ts

import { ServiceAccountEntity } from "../entities/service_account_entity.ts";

/**
 * @description 服务账户仓储接口
 */
export interface IServiceAccountRepository {
	/**
	 * @description 根据ID查找服务账户
	 * @param id 服务账户ID
	 * @returns ServiceAccountEntity 或 null
	 */
	findById(id: string): Promise<ServiceAccountEntity | null>;

	/**
	 * @description 根据账号查找服务账户
	 * @param account 账号
	 * @returns ServiceAccountEntity 或 null
	 */
	findByAccount(account: string): Promise<ServiceAccountEntity | null>;

	/**
	 * @description 保存服务账户
	 * @param serviceAccount 服务账户实体
	 * @returns 保存后的服务账户实体
	 */
	save(serviceAccount: ServiceAccountEntity): Promise<ServiceAccountEntity>;

	/**
	 * @description 更新服务账户
	 * @param serviceAccount 服务账户实体
	 * @returns 更新后的服务账户实体
	 */
	update(serviceAccount: ServiceAccountEntity): Promise<ServiceAccountEntity>;

	/**
	 * @description 删除服务账户
	 * @param id 服务账户ID
	 * @returns 是否删除成功
	 */
	delete(id: string): Promise<boolean>;

	/**
	 * @description 获取所有服务账户
	 * @returns 服务账户列表
	 */
	findAll(): Promise<ServiceAccountEntity[]>;
}
