/**
 * @description 业务错误代码枚举
 * 成功码段: 20000 - 29999
 * 客户端错误码段: 40000 - 49999
 * 服务端错误码段: 50000 - 59999
 */
export enum BusinessCode {
  // --- 成功码段 (2xxxx) ---
  SUCCESS = 20000, // 操作成功
  CREATED = 20100, // 资源创建成功

  // --- 客户端错误码段 (4xxxx) ---
  // 通用客户端错误
  VALIDATION_ERROR = 40000, // 参数校验错误
  BAD_REQUEST = 40001,      // 错误的请求

  // 认证与授权错误
  UNAUTHENTICATED = 40100, // 未认证（未登录）
  LOGIN_FAILED = 40101,    // 登录失败
  TOKEN_EXPIRED = 40102,   // Token 已过期
  INVALID_TOKEN = 40103,   // 无效 Token
  USER_NOT_REGISTERED = 40104, // 用户未注册

  UNAUTHORIZED = 40300,    // 未授权（无权限访问）

  NOT_FOUND = 40400,       // 资源未找到
  USER_NOT_FOUND = 40401,  // 用户未找到

  CONFLICT = 40900,        // 请求冲突
  EMAIL_ALREADY_EXISTS = 40901, // 邮箱已存在
  PHONE_NUMBER_ALREADY_EXISTS = 40902, // 手机号码已存在

  // --- 服务端错误码段 (5xxxx) ---
  UNKNOWN_ERROR = 50000,   // 未知错误
  DATABASE_ERROR = 50001,  // 数据库操作错误
  INTERNAL_SERVER_ERROR = 50002, // 服务器内部错误

  // 更多自定义业务错误...
}

/**
 * @description 业务错误码对应的 HTTP 状态码
 */
export const CodeHttpStatus: Record<BusinessCode, number> = {
  // 成功
  [BusinessCode.SUCCESS]: 200,
  [BusinessCode.CREATED]: 201,

  // 客户端错误
  [BusinessCode.VALIDATION_ERROR]: 400,
  [BusinessCode.BAD_REQUEST]: 400,
  [BusinessCode.UNAUTHENTICATED]: 401,
  [BusinessCode.LOGIN_FAILED]: 401,
  [BusinessCode.TOKEN_EXPIRED]: 401,
  [BusinessCode.INVALID_TOKEN]: 401,
  [BusinessCode.USER_NOT_REGISTERED]: 401,

  [BusinessCode.UNAUTHORIZED]: 403,
  [BusinessCode.NOT_FOUND]: 404,
  [BusinessCode.USER_NOT_FOUND]: 404,
  [BusinessCode.CONFLICT]: 409,
  [BusinessCode.EMAIL_ALREADY_EXISTS]: 409,
  [BusinessCode.PHONE_NUMBER_ALREADY_EXISTS]: 409, // 同样使用 409 Conflict

  // 服务端错误
  [BusinessCode.UNKNOWN_ERROR]: 500,
  [BusinessCode.DATABASE_ERROR]: 500,
  [BusinessCode.INTERNAL_SERVER_ERROR]: 500,
};

/**
 * @description 业务错误码对应的默认错误信息
 */
export const CodeMessage: Record<BusinessCode, string> = {
  // 成功
  [BusinessCode.SUCCESS]: '操作成功。',
  [BusinessCode.CREATED]: '资源创建成功。',

  // 客户端错误
  [BusinessCode.VALIDATION_ERROR]: '请求参数校验失败。',
  [BusinessCode.BAD_REQUEST]: '错误的请求。',
  [BusinessCode.UNAUTHENTICATED]: '用户未登录或登录已失效，请重新登录。',
  [BusinessCode.LOGIN_FAILED]: '用户名或密码错误。',
  [BusinessCode.TOKEN_EXPIRED]: '登录凭证已过期，请重新登录。',
  [BusinessCode.INVALID_TOKEN]: '无效的登录凭证。',
  [BusinessCode.USER_NOT_REGISTERED]: '用户未注册，请先授权手机号注册。',
  [BusinessCode.UNAUTHORIZED]: '您没有权限执行此操作。',
  [BusinessCode.NOT_FOUND]: '请求的资源未找到。',
  [BusinessCode.USER_NOT_FOUND]: '用户不存在。',
  [BusinessCode.CONFLICT]: '请求发生冲突。',
  [BusinessCode.EMAIL_ALREADY_EXISTS]: '该邮箱已被注册。',
  [BusinessCode.PHONE_NUMBER_ALREADY_EXISTS]: "该手机号已被注册。",

  // 服务端错误
  [BusinessCode.UNKNOWN_ERROR]: '发生未知错误，请稍后重试。',
  [BusinessCode.DATABASE_ERROR]: '数据库操作失败，请稍后重试。',
  [BusinessCode.INTERNAL_SERVER_ERROR]: '服务器内部错误，请稍后重试。',
};

/**
 * @description 自定义业务异常类
 */
export class BusinessError extends Error {
  public readonly code: BusinessCode;
  public readonly httpStatus: number;
  public readonly details?: unknown;

  constructor(code: BusinessCode, message?: string, details?: unknown) {
    super(message || CodeMessage[code]);
    this.name = 'BusinessError';
    this.code = code;
    this.httpStatus = CodeHttpStatus[code];
    this.details = details;
    Object.setPrototypeOf(this, BusinessError.prototype);
  }
} 