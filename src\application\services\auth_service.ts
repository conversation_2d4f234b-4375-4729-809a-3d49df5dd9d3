import { IUserRepository } from "../../domain/repositories/user_repository.ts";
import { UserAggregate } from "../../domain/aggregates/user_aggregate.ts";
import { PhoneNumberVO } from "../../domain/value_objects/phone_number_vo.ts";
import { UserResponseDTO } from "../dtos/user_dto.ts";
import { WeChatLoginDTO, WeChatRegisterDTO, AuthResponse } from "../dtos/auth_dto.ts";
import { WeChatService } from "../../infrastructure/services/wechat_service.ts";
import { JwtService } from "../../infrastructure/services/jwt_service.ts";
import { BusinessCode, BusinessError } from "../../domain/errors/business_error.ts";

export class AuthService {
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly wechatService: WeChatService,
    private readonly jwtService: JwtService
  ) {}

  public async handleWeChatLogin(dto: WeChatLoginDTO): Promise<AuthResponse> {
    const { openid, unionid } = await this.wechatService.code2Session(dto.code);

    if (!unionid && !openid) {
      throw new BusinessError(BusinessCode.BAD_REQUEST, "未能获取到用户的 OpenID 或 UnionID。");
    }

    const wechatId = unionid || openid;
    const existingUser = await this.userRepository.findByWechatId(wechatId);

    if (!existingUser) {
      throw new BusinessError(BusinessCode.USER_NOT_REGISTERED);
    }
    
    const token = await this.jwtService.createToken(existingUser);
    return {
        token: token,
        user: UserResponseDTO.fromDomain(existingUser),
    };
  }
  
  public async wechatRegister(dto: WeChatRegisterDTO): Promise<AuthResponse> {
    const { code, loginCode } = dto;
    
    // 1. 使用手机号 code 获取手机号信息
    const phoneInfo = await this.wechatService.getPhoneNumber(code);
    const phoneNumber = PhoneNumberVO.create(phoneInfo.purePhoneNumber);

    // 2. 使用登录 code 获取 openid 和 unionid
    const sessionData = await this.wechatService.code2Session(loginCode);
    const wechatId = sessionData.unionid || sessionData.openid;

    if (!wechatId) {
      throw new BusinessError(BusinessCode.BAD_REQUEST, "未能获取到用户的 OpenID 或 UnionID。");
    }

    // 3. 检查手机号是否已关联其他账户
    let user = await this.userRepository.findByPhoneNumber(phoneNumber);

    if (user) {
      // 手机号已存在，绑定 OpenID/UnionID
      user.setWechatId(wechatId);
    } else {
      // 手机号不存在，创建新用户
      const newUserId = await this.userRepository.nextId();
      user = UserAggregate.createNew(newUserId, phoneNumber, wechatId);
    }

    await this.userRepository.save(user);
    const token = await this.jwtService.createToken(user);

    return {
      token,
      user: UserResponseDTO.fromDomain(user),
    };
  }
} 