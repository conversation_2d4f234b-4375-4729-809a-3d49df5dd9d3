// src/infrastructure/database/postgres_client.ts

import { Pool, PoolClient } from "postgres"; // 使用 import_map
// ConnectionObject 类型似乎不直接从 'postgres' 模块导出，或者在当前版本中名称不同。
// 我们将直接使用对象字面量，其结构与 deno-postgres 文档中的连接选项一致。
import { Kysely, PostgresDialect } from "kysely";
import { DatabaseSchema } from "./db_types.ts"; // 导入我们定义的数据库 Schema 类型
import {
  POSTGRES_HOST,
  POSTGRES_PORT,
  POSTGRES_USER,
  POSTGRES_PASSWORD,
  POSTGRES_DB,
  POSTGRES_POOL_SIZE
} from "../../config.ts"; // 从 config.ts 导入配置

// 构建数据库连接对象
// deno-postgres 允许直接传递一个包含连接参数的对象给 Pool 或 Client 构造函数。
// 这个对象的结构通常包括 user, password, database, hostname, port 等。
const dbConnectionConfig = {
  user: POSTGRES_USER,
  password: POSTGRES_PASSWORD,
  database: POSTGRES_DB,
  hostname: POSTGRES_HOST,
  port: POSTGRES_PORT,
};

const POOL_CONNECTIONS = POSTGRES_POOL_SIZE;

let pool: Pool;
let kyselyDb: Kysely<DatabaseSchema>;

// Kysely 的 PostgresDialect 期望一个与 node-postgres (pg) 兼容的 Pool 接口。
// deno-postgres 的 Pool 接口不同，因此我们创建一个适配器来桥接它们。
interface KyselyPostgresPool {
  connect: () => Promise<{
    query: (sql: string, params: readonly unknown[]) => Promise<{ rows: unknown[], rowCount: number }>;
    release: () => void;
  }>;
  end: () => Promise<void>;
}

/**
 * @description 获取数据库连接池实例
 * @returns Pool 连接池实例
 */
function getPool(): Pool {
  if (!pool) {
    try {
      // 使用 ConnectionObject 进行连接池初始化
      pool = new Pool(dbConnectionConfig, POOL_CONNECTIONS, true);
      console.log(`数据库连接池已初始化 (最大连接数: ${POOL_CONNECTIONS}, 主机: ${POSTGRES_HOST}, 数据库: ${POSTGRES_DB})`);
    } catch (error) {
      console.error("数据库连接池初始化失败:", error);
      console.error("请检查您的 .env 文件或环境变量是否正确配置了 POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB, POSTGRES_HOST, POSTGRES_PORT。");
      throw error; // 重新抛出错误，以便上层应用可以捕获
    }
  }
  return pool;
}

/**
 * @description 获取 Kysely 数据库实例
 * @returns Kysely<DatabaseSchema> Kysely 实例
 */
export function getKyselyDb(): Kysely<DatabaseSchema> {
  if (!kyselyDb) {
    // 创建一个适配器，使 deno-postgres 的 Pool 能够被 Kysely 的 PostgresDialect 使用
    const poolAdapter: KyselyPostgresPool = {
      async connect() {
        const client = await getPool().connect();
        return {
          async query(sql: string, params: readonly unknown[]) {
            const result = await client.queryObject(sql, params as any);
            return {
              rows: result.rows,
              rowCount: result.rowCount ?? 0,
            };
          },
          release() {
            client.release();
          },
        };
      },
      async end() {
        await closePool();
      },
    };

    const dialect = new PostgresDialect({
      pool: poolAdapter as any, // 使用适配器并进行类型断言
    });
    kyselyDb = new Kysely<DatabaseSchema>({
      dialect,
    });
    console.log("Kysely 实例已初始化。");
  }
  return kyselyDb;
}

/**
 * @description 从连接池获取一个客户端连接 (主要供 Kysely 内部或特殊场景使用)
 * @returns Promise<PoolClient> 数据库客户端连接
 * @deprecated 推荐使用 getKyselyDb() 进行数据库操作。
 */
export async function getDbClient(): Promise<PoolClient> {
  console.warn("getDbClient() 已被标记为废弃，推荐使用 getKyselyDb() 进行数据库操作。");
  const currentPool = getPool();
  try {
    const client = await currentPool.connect();
    console.log("成功从连接池获取数据库连接。");
    return client;
  } catch (error) {
    console.error("从连接池获取数据库连接失败:", error);
    throw error; // 重新抛出错误
  }
}

/**
 * @description 关闭数据库连接池 (应用退出时调用)
 */
export async function closePool(): Promise<void> {
  if (pool) {
    try {
      await pool.end();
      console.log("数据库连接池已成功关闭。");
    } catch (error) {
      console.error("关闭数据库连接池失败:", error);
    }
  }
}

// 优雅地关闭连接池
if (Deno.build.os !== "windows") { // Windows 不支持 SIGINT/SIGTERM
    Deno.addSignalListener("SIGINT", async () => {
        console.log("接收到 SIGINT 信号，正在关闭数据库连接池...");
        await closePool();
        Deno.exit();
    });

    Deno.addSignalListener("SIGTERM", async () => {
        console.log("接收到 SIGTERM 信号，正在关闭数据库连接池...");
        await closePool();
        Deno.exit();
    });
}

// 确保在程序退出时关闭连接池 (作为备用方案)
// window.addEventListener("unload", async () => {
//   await closePool();
// });

// 立即尝试初始化连接池，以便尽早发现配置问题
getPool();