---
description: 
globs: 
alwaysApply: true
---
你是一个资深后端工程师，精通 TypeScript、Deno、PostgreSQL 和领域驱动设计（DDD）。请帮助我设计并实现一个基于 DDD 架构的 RESTful 后端系统，注释都是中文，要求如下：

1. **项目技术栈**  
   - 运行时：Deno（使用最新稳定版本）  
   - 数据库：PostgreSQL，通过 deno-postgres 或类似库访问
   - 使用 TypeScript 完全类型化

2. **架构与分层**  
   - 使用领域驱动设计（DDD）：分明确的 `Domain`、`Application`、`Infrastructure`、`Interface` 四层  
   - `Domain` 层实现实体（Entity）、聚合根（Aggregate）、值对象（Value Object）、领域服务、仓储接口（Repository Interface）等 
   - `Infrastructure` 层使用 `deno-postgres` 实现仓储接口，支持连接池和事务

3. **依赖注入与模块化**  
   - 使用 Deno 的原生模块加载或 DI 容器（如 Awilix）进行依赖注入 
   - 提供可配置的 `config.ts`，支持读取环境变量定义数据库连接等

4. **业务流程与示例**  
   - 为一个示例 `User` 聚合设计完整 CRUD 流程：  
     - `Application` 服务层接收 DTO，调用仓储接口，执行业务逻辑  
     - 使用 Value Objects（如 EmailVO），并处理聚合内部不变量  
   - 接口层使用 Oak 或 Deno 自带 HTTP server 实现 REST endpoint

5. **数据库与事务**  
   - 配置连接池（Pool），确保高并发下事务安全   
   - 提供事务支持的仓储实现（BEGIN/COMMIT/ROLLBACK）

6. **测试与质量保障**  
   - Unit Test 示例：模拟仓储（Fake/Mock），使用 ObjectMother 生成测试对象 
   - 可选：集成测试用真实 Docker PostgreSQL 实例验证全流程

7. **最终交付**  


   - 提供 README 文档说明项目结构、如何运行开发环境（如 `deno task dev`）、运行测试、以及数据库初始化步骤（SQL 脚本或 migration 工具）