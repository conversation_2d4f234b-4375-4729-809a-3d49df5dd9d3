-- init_db.sql

-- 删除已存在的 users 表 (可选，用于重新初始化)
-- DROP TABLE IF EXISTS users;

-- 创建 users 表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY,
    phone_number VARCHAR(20) UNIQUE NOT NULL, -- 手机号码，唯一且不能为空
    password VARCHAR(255), -- 用户密码哈希，可以为空
    wechat_union_id VARCHAR(255) UNIQUE, -- 微信统一ID，可以为空但必须唯一
    wechat_openid VARCHAR(255) UNIQUE, -- 微信开放ID，可以为空但必须唯一
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 为 updated_at 创建触发器，使其在行更新时自动更新 (可选，但推荐)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 确保触发器只在表存在时创建
DO $$
BEGIN
   IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
      DROP TRIGGER IF EXISTS update_users_updated_at ON users;
      CREATE TRIGGER update_users_updated_at
      BEFORE UPDATE ON users
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
   END IF;
END $$;


-- 兼容旧版本：如果表已存在，则尝试添加新列
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        ALTER TABLE users
        ADD COLUMN IF NOT EXISTS wechat_union_id VARCHAR(255) UNIQUE,
        ADD COLUMN IF NOT EXISTS wechat_openid VARCHAR(255) UNIQUE;
    END IF;
END $$;


-- 索引建议 (根据查询模式添加)
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);
CREATE INDEX IF NOT EXISTS idx_users_wechat_union_id ON users(wechat_union_id);
CREATE INDEX IF NOT EXISTS idx_users_wechat_openid ON users(wechat_openid);

-- 初始数据示例 (可选)
-- INSERT INTO users (id, phone_number, password) VALUES (gen_random_uuid(), '***********', 'hashed_password_here');


SELECT '数据库表 users 初始化完成。';


-- 创建 service_accounts 表
CREATE TABLE IF NOT EXISTS service_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account VARCHAR(255) UNIQUE NOT NULL, -- 账号，唯一且不能为空
    token VARCHAR(500) NOT NULL, -- 登录凭证，不能为空
    expiry_time TIMESTAMP WITH TIME ZONE, -- 过期时间，可以为空
    service_count INTEGER DEFAULT 0 CHECK (service_count >= 0 AND service_count <= 10), -- 调用次数，0~10
    service_time TIMESTAMP WITH TIME ZONE, -- 调用的时间，可以为空
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 为 service_accounts 表的 updated_at 创建触发器
DO $$
BEGIN
   IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'service_accounts') THEN
      DROP TRIGGER IF EXISTS update_service_accounts_updated_at ON service_accounts;
      CREATE TRIGGER update_service_accounts_updated_at
      BEFORE UPDATE ON service_accounts
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
   END IF;
END $$;

-- 为 service_accounts 表创建索引
CREATE INDEX IF NOT EXISTS idx_service_accounts_account ON service_accounts(account);
CREATE INDEX IF NOT EXISTS idx_service_accounts_token ON service_accounts(token);
CREATE INDEX IF NOT EXISTS idx_service_accounts_expiry_time ON service_accounts(expiry_time);

SELECT '数据库表 service_accounts 初始化完成。';