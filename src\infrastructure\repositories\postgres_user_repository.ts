// src/infrastructure/repositories/postgres_user_repository.ts

import { v7 as uuidv7 } from "uuid";
import { getKyselyDb } from "../database/postgres_client.ts";
import { IUserRepository } from "../../domain/repositories/user_repository.ts";
import { UserAggregate } from "../../domain/aggregates/user_aggregate.ts";
import { PhoneNumberVO } from "../../domain/value_objects/phone_number_vo.ts";
import { UsersTable } from "../database/db_types.ts";
import { Insertable, Selectable, Updateable } from "kysely";

export class PostgresUserRepository implements IUserRepository {
  private db = getKyselyDb();

  async nextId(): Promise<string> {
    return Promise.resolve(uuidv7());
  }

  async findByPhoneNumber(phoneNumber: PhoneNumberVO): Promise<UserAggregate | null> {
    const record = await this.db
      .selectFrom("users")
      .selectAll()
      .where("phone_number", "=", phoneNumber.value)
      .executeTakeFirst();

    if (!record) {
      return null;
    }
    return this.toDomain(record);
  }

  async findById(id: string): Promise<UserAggregate | null> {
    const record = await this.db
      .selectFrom("users")
      .selectAll()
      .where("id", "=", id)
      .executeTakeFirst();

    if (!record) {
      return null;
    }
    return this.toDomain(record);
  }

  async findByWechatId(wechatId: string): Promise<UserAggregate | null> {
    const record = await this.db
      .selectFrom("users")
      .selectAll()
      .where((eb) => eb.or([
        eb("wechat_union_id", "=", wechatId),
        eb("wechat_openid", "=", wechatId)
      ]))
      .executeTakeFirst();

    if (!record) {
      return null;
    }
    return this.toDomain(record);
  }

  async save(user: UserAggregate): Promise<void> {
    // Kysely 的 'onConflict' 或 'upsert' 在这里是更好的选择，但为了保持示例简单，我们使用 find-then-update/insert
    const existing = await this.db.selectFrom("users").where("id", "=", user.id).executeTakeFirst();
    
    if (existing) {
      const userDataForUpdate = this.toPersistenceForUpdate(user);
      await this.db
        .updateTable("users")
        .set(userDataForUpdate)
        .where("id", "=", user.id)
        .execute();
    } else {
      const userDataForInsert = this.toPersistenceForInsert(user);
      await this.db.insertInto("users").values(userDataForInsert).execute();
    }
  }

  async delete(id: string): Promise<void> {
    await this.db.deleteFrom("users").where("id", "=", id).execute();
  }

  private toDomain(record: Selectable<UsersTable>): UserAggregate {
    return UserAggregate.reconstitute(
      record.id,
      PhoneNumberVO.create(record.phone_number),
      record.password ?? undefined,
      record.created_at, // Kysely/deno-postgres 方言会处理为 Date 对象
      record.updated_at, // Kysely/deno-postgres 方言会处理为 Date 对象
      record.wechat_union_id ?? undefined,
      record.wechat_openid ?? undefined
    );
  }

  private toPersistenceForInsert(user: UserAggregate): Insertable<UsersTable> {
    return {
      id: user.id,
      phone_number: user.phoneNumber.value,
      password: user.passwordHash ?? null,
      wechat_union_id: user.wechatUnionId ?? null,
      wechat_openid: user.wechatOpenId ?? null,
    };
  }
  
  private toPersistenceForUpdate(user: UserAggregate): Updateable<UsersTable> {
    return {
      phone_number: user.phoneNumber.value,
      password: user.passwordHash,
      wechat_union_id: user.wechatUnionId,
      wechat_openid: user.wechatOpenId,
    };
  }
} 