import { UserResponseDTO } from "./user_dto.ts";

/**
 * @description 微信 code 登录的 DTO
 */
export class WeChatLoginDTO {
  code!: string;
}

/**
 * @description 微信注册的 DTO
 */
export class WeChatRegisterDTO {
  /**
   * @description 用于获取手机号的 code
   */
  code!: string;
  /**
   * @description 用于获取 openid/unionid 的登录 code
   */
  loginCode!: string;
}

/**
 * @description 成功登录或注册后的统一响应
 */
export interface AuthResponse {
  token: string;
  user: UserResponseDTO;
} 