import { container } from "../../../container.ts";
import { WeChatLoginDTO, WeChatRegisterDTO } from "../../../application/dtos/auth_dto.ts";
import { Controller, Post, ControllerMethodArgs } from "@dklab/oak-routing-ctrl";
import { BusinessCode, BusinessError } from "../../../domain/errors/business_error.ts";
import { 
  WeChatLoginSchema,
  WeChatRegisterSchema,
  AuthResponseSchema
} from "../schemas/auth_schemas.ts";

const wechatLoginSpec = {
  summary: "微信登录",
  description: "使用 wx.login() 获取的 code 进行登录。如果用户已注册，直接返回 token；如果未注册，返回 401 错误，提示前端进行注册。",
  request: { body: { content: { "application/json": { schema: WeChatLoginSchema }}}},
  responses: { "200": { description: "登录成功", content: { "application/json": { schema: AuthResponseSchema }}}},
};

const wechatRegisterSpec = {
  summary: "微信授权手机号注册",
  description: "使用 wx.login() 获取的 loginCode 和 getPhoneNumber 获取的 code 来注册新用户或绑定微信到现有用户。",
  request: { body: { content: { "application/json": { schema: WeChatRegisterSchema }}}},
  responses: { "200": { description: "注册成功", content: { "application/json": { schema: AuthResponseSchema }}}},
};

@Controller("/api/v1/auth")
export class AuthController {
  private authService = container.authService;

  @Post("/wechat/login", wechatLoginSpec)
  @ControllerMethodArgs("body")
  public async wechatLogin(dto: WeChatLoginDTO) {
    try {
      const validatedDto = WeChatLoginSchema.parse(dto);
      const result = await this.authService.handleWeChatLogin(validatedDto);
      return {
        success: true,
        code: BusinessCode.SUCCESS,
        message: "登录成功",
        data: result,
      };
    } catch (error) {
      if (error instanceof BusinessError) {
        return {
          success: false,
          code: error.code,
          message: error.message,
          data: null,
        };
      }
      throw error;
    }
  }

  @Post("/wechat/register", wechatRegisterSpec)
  @ControllerMethodArgs("body")
  public async wechatRegister(dto: WeChatRegisterDTO) {
    try {
      const validatedDto = WeChatRegisterSchema.parse(dto);
      const result = await this.authService.wechatRegister(validatedDto);
      return {
        success: true,
        code: BusinessCode.SUCCESS,
        message: "注册成功",
        data: result,
      };
    } catch (error) {
      if (error instanceof BusinessError) {
        return {
          success: false,
          code: error.code,
          message: error.message,
          data: null,
        };
      }
      throw error;
    }
  }
}