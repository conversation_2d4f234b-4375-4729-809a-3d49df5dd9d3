// src/application/dtos/user_dtos.ts

/**
 * @description 创建用户的 DTO
 */
export interface CreateUserDto {
  phoneNumber: string; // 手机号码字符串，应用服务层会转换为 PhoneNumberVO
  // 可以添加其他注册时需要的字段，例如：password, verificationCode 等
}

/**
 * @description 用户信息的 DTO (用于响应)
 */
export interface UserDto {
  id: string;
  phoneNumber: string; // 直接返回字符串格式的手机号
  createdAt: string; // ISO 格式的日期字符串
  updatedAt: string; // ISO 格式的日期字符串
}

/**
 * @description 更新用户手机号的 DTO
 */
export interface UpdateUserPhoneNumberDto {
  newPhoneNumber: string;
  // 可能需要旧手机号或验证码进行验证
}

/**
 * @description 更新用户微信 OpenID 的 DTO
 */
export interface UpdateUserWechatOpenIdDto {
  wechatOpenId: string;
}