// src/interface/http/middlewares/service_account_auth_middleware.ts

import { Context, Next } from "oak";
import { SERVICE_ACCOUNT_SESSION } from "../../../config.ts";

/**
 * @description 服务账户授权中间件
 * 验证请求头中的Authorization是否为配置的常量session
 */
export async function serviceAccountAuthMiddleware(ctx: Context, next: Next) {
	const authorization = ctx.request.headers.get("Authorization");
	
	if (!authorization) {
		ctx.response.status = 401;
		ctx.response.body = {
			success: false,
			code: 40100,
			message: "缺少Authorization头",
			data: null,
		};
		return;
	}

	// 检查Authorization是否匹配配置的session
	if (authorization !== SERVICE_ACCOUNT_SESSION) {
		ctx.response.status = 401;
		ctx.response.body = {
			success: false,
			code: 40101,
			message: "无效的Authorization",
			data: null,
		};
		return;
	}

	// 验证通过，继续执行下一个中间件
	await next();
}
