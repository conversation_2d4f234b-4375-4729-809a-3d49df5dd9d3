// src/config.ts

// 环境变量将通过 Deno 运行时的 --allow-env 和 --env 参数注入
// 例如: deno run --allow-net --allow-read --allow-env --env=.env src/main.ts

function getEnvVar(key: string, defaultValue?: string): string {
	const value = Deno.env.get(key);
	if (value === undefined && defaultValue === undefined) {
		console.warn(`警告: 环境变量 ${key} 未设置，且没有提供默认值。`);
		// 在某些情况下，你可能希望抛出错误而不是返回 undefined 或空字符串
		// throw new Error(`环境变量 ${key} 未设置。`);
		return ""; // 或者返回一个空字符串或特定的哨兵值
	}
	// 如果 value 是 undefined，并且 defaultValue 存在，则返回 defaultValue
	// 否则返回 value (即使它是 undefined，如果 defaultValue 也是 undefined)
	return value === undefined ? defaultValue! : value;
}

export const APP_PORT = parseInt(getEnvVar("APP_PORT", "8000"), 10);

// 数据库配置
export const POSTGRES_HOST = getEnvVar("POSTGRES_HOST", "localhost");
export const POSTGRES_PORT = parseInt(getEnvVar("POSTGRES_PORT", "5432"), 10);
export const POSTGRES_USER = getEnvVar("POSTGRES_USER", "postgres"); // 默认用户名
export const POSTGRES_PASSWORD = getEnvVar("POSTGRES_PASSWORD", "password"); // 默认密码，生产环境务必修改
export const POSTGRES_DB = Deno.env.get("POSTGRES_DB") || "inno_mark_db";
export const POSTGRES_POOL_SIZE = parseInt(Deno.env.get("POSTGRES_POOL_SIZE") || "10", 10);

// 检查关键配置是否缺失 (可选，但推荐)
if (!POSTGRES_USER || !POSTGRES_PASSWORD || !POSTGRES_DB) {
	console.error(
		"错误: 数据库连接配置不完整 (POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DB)。请检查您的 .env 文件或环境变量。"
	);
	// 根据需要，这里可以 Deno.exit(1) 来终止应用启动
}

// --- 微信小程序配置 ---
export const WECHAT_APP_ID = Deno.env.get("WECHAT_APP_ID") || "";
export const WECHAT_APP_SECRET = Deno.env.get("WECHAT_APP_SECRET") || "";

// --- JWT 配置 ---
export const JWT_SECRET_KEY = Deno.env.get("JWT_SECRET_KEY") || "your-super-secret-and-long-key-for-jwt";
export const JWT_EXPIRATION_SECONDS = parseInt(Deno.env.get("JWT_EXPIRATION_SECONDS") || "86400", 10); // 24 hours

if (!WECHAT_APP_ID || !WECHAT_APP_SECRET) {
	console.warn("警告: 微信小程序的 APP_ID 或 APP_SECRET 未配置。微信登录功能将无法正常工作。");
}

if (JWT_SECRET_KEY === "your-super-secret-and-long-key-for-jwt") {
	console.warn("警告: 你正在使用默认的 JWT_SECRET_KEY。请在 .env 文件中设置一个强壮的密钥以确保安全。");
}

// 其他应用配置可以放在这里
// export const JWT_SECRET = getEnvVar("JWT_SECRET", "your-secret-key");
export const SERVICE_API_PATH = Deno.env.get("SERVICE_API_PATH") || "localhost";
