// test_api.ts - 测试第三方账号管理接口

const BASE_URL = "http://localhost:8000";
const AUTH_TOKEN = "your-secure-session-token-here"; // 从环境变量中获取的授权令牌

interface ApiResponse {
  success: boolean;
  code: number;
  message: string;
  data: any;
}

/**
 * 测试基础连通性
 */
async function testPing() {
  console.log("🔍 测试基础连通性...");
  try {
    const response = await fetch(`${BASE_URL}/api/v1/ping`);
    const data = await response.json();
    console.log("✅ Ping 测试成功:", data);
    return true;
  } catch (error) {
    console.error("❌ Ping 测试失败:", error);
    return false;
  }
}

/**
 * 测试添加服务账户
 */
async function testAddServiceAccount() {
  console.log("\n🔍 测试添加服务账户...");
  try {
    const response = await fetch(`${BASE_URL}/api/v1/service-accounts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": AUTH_TOKEN,
      },
      body: JSON.stringify({
        account: "<EMAIL>"
      }),
    });

    const data: ApiResponse = await response.json();
    console.log("📝 添加服务账户响应:", data);
    
    if (data.success) {
      console.log("✅ 添加服务账户成功");
      return data.data;
    } else {
      console.log("⚠️ 添加服务账户失败:", data.message);
      return null;
    }
  } catch (error) {
    console.error("❌ 添加服务账户请求失败:", error);
    return null;
  }
}

/**
 * 测试获取所有服务账户
 */
async function testGetAllServiceAccounts() {
  console.log("\n🔍 测试获取所有服务账户...");
  try {
    const response = await fetch(`${BASE_URL}/api/v1/service-accounts`, {
      method: "GET",
      headers: {
        "Authorization": AUTH_TOKEN,
      },
    });

    const data: ApiResponse = await response.json();
    console.log("📝 获取所有服务账户响应:", data);
    
    if (data.success) {
      console.log("✅ 获取所有服务账户成功");
      console.log(`📊 共找到 ${data.data.total} 个服务账户`);
      return data.data;
    } else {
      console.log("⚠️ 获取所有服务账户失败:", data.message);
      return null;
    }
  } catch (error) {
    console.error("❌ 获取所有服务账户请求失败:", error);
    return null;
  }
}

/**
 * 测试根据ID获取服务账户
 */
async function testGetServiceAccountById(id: string) {
  console.log(`\n🔍 测试根据ID获取服务账户 (${id})...`);
  try {
    const response = await fetch(`${BASE_URL}/api/v1/service-accounts/${id}`, {
      method: "GET",
      headers: {
        "Authorization": AUTH_TOKEN,
      },
    });

    const data: ApiResponse = await response.json();
    console.log("📝 根据ID获取服务账户响应:", data);
    
    if (data.success) {
      console.log("✅ 根据ID获取服务账户成功");
      return data.data;
    } else {
      console.log("⚠️ 根据ID获取服务账户失败:", data.message);
      return null;
    }
  } catch (error) {
    console.error("❌ 根据ID获取服务账户请求失败:", error);
    return null;
  }
}

/**
 * 测试授权验证
 */
async function testAuthValidation() {
  console.log("\n🔍 测试授权验证...");
  try {
    // 测试无Authorization头
    const response1 = await fetch(`${BASE_URL}/api/v1/service-accounts`, {
      method: "GET",
    });
    const data1: ApiResponse = await response1.json();
    console.log("📝 无Authorization头响应:", data1);

    // 测试错误的Authorization头
    const response2 = await fetch(`${BASE_URL}/api/v1/service-accounts`, {
      method: "GET",
      headers: {
        "Authorization": "wrong-token",
      },
    });
    const data2: ApiResponse = await response2.json();
    console.log("📝 错误Authorization头响应:", data2);

    if (!data1.success && !data2.success) {
      console.log("✅ 授权验证工作正常");
      return true;
    } else {
      console.log("⚠️ 授权验证可能有问题");
      return false;
    }
  } catch (error) {
    console.error("❌ 授权验证测试失败:", error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log("🚀 开始测试第三方账号管理接口...\n");

  // 1. 测试基础连通性
  const pingSuccess = await testPing();
  if (!pingSuccess) {
    console.log("❌ 服务器连接失败，停止测试");
    return;
  }

  // 2. 测试授权验证
  await testAuthValidation();

  // 3. 测试添加服务账户
  const newAccount = await testAddServiceAccount();

  // 4. 测试获取所有服务账户
  await testGetAllServiceAccounts();

  // 5. 如果成功添加了账户，测试根据ID获取
  if (newAccount && newAccount.id) {
    await testGetServiceAccountById(newAccount.id);
  }

  console.log("\n🎉 测试完成！");
}

// 运行测试
if (import.meta.main) {
  runTests().catch(console.error);
}
