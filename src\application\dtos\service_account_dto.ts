// src/application/dtos/service_account_dto.ts

/**
 * @description 添加服务账户请求DTO
 */
export interface AddServiceAccountDTO {
	account: string;
}

/**
 * @description 服务账户响应DTO
 */
export interface ServiceAccountResponseDTO {
	id: string;
	account: string;
	serviceCount: number;
	serviceTime: string; // ISO 字符串格式
	expiryTime: string; // ISO 字符串格式
	createdAt: string; // ISO 字符串格式
	updatedAt: string; // ISO 字符串格式
}

/**
 * @description 服务账户列表响应DTO
 */
export interface ServiceAccountListResponseDTO {
	accounts: ServiceAccountResponseDTO[];
	total: number;
}
