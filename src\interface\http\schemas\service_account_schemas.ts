// src/interface/http/schemas/service_account_schemas.ts

import { z } from "zod";

/**
 * @description 添加服务账户请求schema
 */
export const AddServiceAccountSchema = z.object({
	account: z.string()
		.min(1, "账号不能为空")
		.max(255, "账号长度不能超过255个字符")
		.email("账号必须是有效的邮箱格式"),
});

/**
 * @description 服务账户响应schema
 */
export const ServiceAccountResponseSchema = z.object({
	id: z.string().uuid("ID必须是有效的UUID格式"),
	account: z.string(),
	serviceCount: z.number().int().min(0).max(10),
	serviceTime: z.string().datetime("服务时间必须是有效的ISO日期时间格式"),
	expiryTime: z.string().datetime("过期时间必须是有效的ISO日期时间格式"),
	createdAt: z.string().datetime("创建时间必须是有效的ISO日期时间格式"),
	updatedAt: z.string().datetime("更新时间必须是有效的ISO日期时间格式"),
});

/**
 * @description 服务账户列表响应schema
 */
export const ServiceAccountListResponseSchema = z.object({
	accounts: z.array(ServiceAccountResponseSchema),
	total: z.number().int().min(0),
});

/**
 * @description API响应基础schema
 */
export const ServiceAccountApiResponseSchema = z.object({
	success: z.boolean(),
	code: z.number(),
	message: z.string(),
	data: ServiceAccountResponseSchema.nullable(),
});

/**
 * @description API列表响应schema
 */
export const ServiceAccountListApiResponseSchema = z.object({
	success: z.boolean(),
	code: z.number(),
	message: z.string(),
	data: ServiceAccountListResponseSchema.nullable(),
});
