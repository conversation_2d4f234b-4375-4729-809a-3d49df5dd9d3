import { z } from "@dklab/oak-routing-ctrl";
import { UserResponseSchema } from "./user_schemas.ts";

// --- 基础响应结构 ---
const SuccessResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.literal(true),
    code: z.number().int().positive().optional().default(20000),
    message: z.string().optional().default("操作成功"),
    data: dataSchema,
  });


// --- 业务 Schema ---
export const WeChatLoginSchema = z.object({
  code: z.string({ required_error: "code 不能为空" }),
});

export const WeChatRegisterSchema = z.object({
  code: z.string({ required_error: "获取手机号的 code 不能为空" }),
  loginCode: z.string({ required_error: "登录用的 loginCode 不能为空" }),
});

export const AuthResponseDataSchema = z.object({
  token: z.string(),
  user: UserResponseSchema,
});

export const AuthResponseSchema = SuccessResponseSchema(AuthResponseDataSchema); 