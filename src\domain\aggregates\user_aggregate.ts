// src/domain/aggregates/user_aggregate.ts

import { UserEntity, IUserEntity } from "../entities/user_entity.ts";
import { PhoneNumberVO } from "../value_objects/phone_number_vo.ts";

/**
 * @description 用户聚合根接口，目前与 IUserEntity 相同
 * 未来如果聚合更复杂，可以扩展此接口
 */
export interface IUserAggregate extends IUserEntity {}

/**
 * @description 用户聚合根类
 * 在这个简单的用户场景中，UserEntity 本身可以作为聚合根。
 * 如果用户聚合包含更多实体（如用户配置、用户地址列表等），
 * 则 UserAggregate 会封装 UserEntity 以及其他相关实体和值对象，并维护它们之间的一致性。
 */
export class UserAggregate extends UserEntity implements IUserAggregate {
  private constructor(
    id: string,
    phoneNumber: PhoneNumberVO,
    passwordHash?: string,
    wechatUnionId?: string,
    wechatOpenId?: string,
    createdAt?: Date,
    updatedAt?: Date
  ) {
    super(id, phoneNumber, passwordHash, wechatUnionId, wechatOpenId, createdAt, updatedAt);
  }

  /**
   * @description 创建一个新的用户聚合实例
   * @param id 用户ID
   * @param phoneNumber 手机号码
   * @param wechatId 微信ID (OpenID 或 UnionID)
   * @param passwordHash 密码哈希
   * @returns UserAggregate 实例
   */
  public static createNew(
    id: string,
    phoneNumber: PhoneNumberVO,
    wechatId: string,
    passwordHash?: string,
  ): UserAggregate {
    const isUnionId = wechatId.startsWith("o"); // 这是一个非常简化的判断
    const unionId = isUnionId ? wechatId : undefined;
    const openId = !isUnionId ? wechatId : undefined;
    
    return new UserAggregate(id, phoneNumber, passwordHash, unionId, openId);
  }

  /**
   * @description 从持久化数据恢复用户聚合实例
   * @param id 用户ID
   * @param phoneNumber 手机号码
   * @param passwordHash 密码哈希
   * @param createdAt 创建时间
   * @param updatedAt 更新时间
   * @param wechatUnionId 微信统一ID
   * @param wechatOpenId 微信开放ID
   * @returns UserAggregate 实例
   */
  public static reconstitute(
    id: string,
    phoneNumber: PhoneNumberVO,
    passwordHash: string | undefined,
    createdAt: Date,
    updatedAt: Date,
    wechatUnionId?: string,
    wechatOpenId?: string
  ): UserAggregate {
    return new UserAggregate(id, phoneNumber, passwordHash, wechatUnionId, wechatOpenId, createdAt, updatedAt);
  }

  /**
   * @description 设置微信ID (OpenID 或 UnionID)
   * @param wechatId 微信ID
   */
  public setWechatId(wechatId: string): void {
    // 简单判断 o- 开头的是 unionid，否则是 openid
    // 在实际场景中，微信的 openid 和 unionid 格式有更明确的区别
    if (wechatId.startsWith("o")) {
      this.wechatUnionId = wechatId;
    } else {
      this.wechatOpenId = wechatId;
    }
    this.updatedAt = new Date();
  }

  // 可以在聚合根层面添加更多跨多个实体的业务方法
  // 例如：deactivateUser(), verifyPhoneNumber() 等
}