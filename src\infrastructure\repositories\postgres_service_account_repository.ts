// src/infrastructure/repositories/postgres_service_account_repository.ts

import { ServiceAccountEntity } from "../../domain/entities/service_account_entity.ts";
import { IServiceAccountRepository } from "../../domain/repositories/service_account_repository.ts";
import { getPostgresClient } from "../database/postgres_client.ts";

/**
 * @description PostgreSQL 服务账户仓储实现
 */
export class PostgresServiceAccountRepository implements IServiceAccountRepository {
	/**
	 * @description 根据ID查找服务账户
	 */
	async findById(id: string): Promise<ServiceAccountEntity | null> {
		const client = await getPostgresClient();
		try {
			const result = await client.queryObject<{
				id: string;
				account: string;
				token: string;
				expiry_time: Date;
				service_count: number;
				service_time: Date;
				created_at: Date;
				updated_at: Date;
			}>(
				"SELECT * FROM service_accounts WHERE id = $1",
				[id]
			);

			if (result.rows.length === 0) {
				return null;
			}

			return ServiceAccountEntity.fromData(result.rows[0]);
		} finally {
			client.release();
		}
	}

	/**
	 * @description 根据账号查找服务账户
	 */
	async findByAccount(account: string): Promise<ServiceAccountEntity | null> {
		const client = await getPostgresClient();
		try {
			const result = await client.queryObject<{
				id: string;
				account: string;
				token: string;
				expiry_time: Date;
				service_count: number;
				service_time: Date;
				created_at: Date;
				updated_at: Date;
			}>(
				"SELECT * FROM service_accounts WHERE account = $1",
				[account]
			);

			if (result.rows.length === 0) {
				return null;
			}

			return ServiceAccountEntity.fromData(result.rows[0]);
		} finally {
			client.release();
		}
	}

	/**
	 * @description 保存服务账户
	 */
	async save(serviceAccount: ServiceAccountEntity): Promise<ServiceAccountEntity> {
		const client = await getPostgresClient();
		try {
			const result = await client.queryObject<{
				id: string;
				account: string;
				token: string;
				expiry_time: Date;
				service_count: number;
				service_time: Date;
				created_at: Date;
				updated_at: Date;
			}>(
				`INSERT INTO service_accounts 
				(id, account, token, expiry_time, service_count, service_time, created_at, updated_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
				RETURNING *`,
				[
					serviceAccount.id,
					serviceAccount.account,
					serviceAccount.token,
					serviceAccount.expiryTime,
					serviceAccount.serviceCount,
					serviceAccount.serviceTime,
					serviceAccount.createdAt,
					serviceAccount.updatedAt,
				]
			);

			return ServiceAccountEntity.fromData(result.rows[0]);
		} finally {
			client.release();
		}
	}

	/**
	 * @description 更新服务账户
	 */
	async update(serviceAccount: ServiceAccountEntity): Promise<ServiceAccountEntity> {
		const client = await getPostgresClient();
		try {
			const result = await client.queryObject<{
				id: string;
				account: string;
				token: string;
				expiry_time: Date;
				service_count: number;
				service_time: Date;
				created_at: Date;
				updated_at: Date;
			}>(
				`UPDATE service_accounts 
				SET account = $2, token = $3, expiry_time = $4, service_count = $5, 
				    service_time = $6, updated_at = $7
				WHERE id = $1
				RETURNING *`,
				[
					serviceAccount.id,
					serviceAccount.account,
					serviceAccount.token,
					serviceAccount.expiryTime,
					serviceAccount.serviceCount,
					serviceAccount.serviceTime,
					serviceAccount.updatedAt,
				]
			);

			if (result.rows.length === 0) {
				throw new Error(`Service account with id ${serviceAccount.id} not found`);
			}

			return ServiceAccountEntity.fromData(result.rows[0]);
		} finally {
			client.release();
		}
	}

	/**
	 * @description 删除服务账户
	 */
	async delete(id: string): Promise<boolean> {
		const client = await getPostgresClient();
		try {
			const result = await client.queryObject(
				"DELETE FROM service_accounts WHERE id = $1",
				[id]
			);

			return result.rowCount !== undefined && result.rowCount > 0;
		} finally {
			client.release();
		}
	}

	/**
	 * @description 获取所有服务账户
	 */
	async findAll(): Promise<ServiceAccountEntity[]> {
		const client = await getPostgresClient();
		try {
			const result = await client.queryObject<{
				id: string;
				account: string;
				token: string;
				expiry_time: Date;
				service_count: number;
				service_time: Date;
				created_at: Date;
				updated_at: Date;
			}>("SELECT * FROM service_accounts ORDER BY created_at DESC");

			return result.rows.map(row => ServiceAccountEntity.fromData(row));
		} finally {
			client.release();
		}
	}
}
