// src/interface/http/schemas/user_schemas.ts

import { z } from "@dklab/oak-routing-ctrl";

export const RegisterUserSchema = z.object({
  phoneNumber: z.string({ required_error: "phoneNumber 不能为空" })
    .regex(/^1[3-9]\d{9}$/, "无效的手机号码格式"),
  password: z.string().min(6, "密码长度不能少于6位").optional(),
  wechatUnionId: z.string().optional(),
});

export const UserResponseSchema = z.object({
  id: z.string().uuid(),
  phoneNumber: z.string(),
  wechatUnionId: z.string().optional().nullable(),
  createdAt: z.string().datetime().describe("创建时间"),
  updatedAt: z.string().datetime().describe("更新时间"),
}); 