// src/infrastructure/database/db_types.ts

import { ColumnType, Generated } from 'kysely';

/**
 * 定义数据库中所有表的接口
 */
export interface DatabaseSchema {
  users: UsersTable;
  // 如果有其他表，在这里添加，例如：
  // posts: PostsTable;
}

/**
 * @description 'users' 表的接口定义
 */
export interface UsersTable {
  /**
   * 用户ID (UUID)，主键
   */
  id: string; // UUID 通常存储为字符串

  /**
   * 用户手机号码，唯一
   */
  phone_number: string;

  /**
   * 用户密码的哈希值
   */
  password: string | null;

  /**
   * 微信统一ID，唯一且可以为空
   */
  wechat_union_id: string | null;

  /**
   * 微信开放ID，唯一且可以为空
   */
  wechat_openid: string | null;

  /**
   * 记录创建时间戳
   * 使用 Generated<Date> 表示该列由数据库自动生成 (例如 DEFAULT CURRENT_TIMESTAMP)
   */
  created_at: ColumnType<Date, string | undefined, string | undefined>;

  /**
   * 记录最后更新时间戳
   * 使用 Generated<Date> 表示该列由数据库自动生成或通过触发器更新
   */
  updated_at: ColumnType<Date, string | undefined, string | undefined>;
}

// 如果有其他表，也在这里定义它们的接口，例如 PostsTable
/*
export interface PostsTable {
  id: Generated<number>;
  title: string;
  content: string;
  user_id: string; // 外键，关联到 users.id
  created_at: ColumnType<Date, string | undefined, string | undefined>;
}
*/