import "reflect-metadata"; // 必须在所有代码之前导入
import { Application } from "oak";
import { useOakServer, useOas } from "@dklab/oak-routing-ctrl";
import { APP_PORT } from "./config.ts";

// --- 导入所有控制器和中间件 ---
import { UserController } from "./interface/http/controllers/user_controller.ts";
import { AuthController } from "./interface/http/controllers/auth_controller.ts";
import { ServiceAccountController } from "./interface/http/controllers/service_account_controller.ts";
import { PingController } from "./interface/http/controllers/ping_controller.ts";
import { jsonErrorHandler } from "./interface/http/middlewares/json_error_handler.ts";

const app = new Application();
const port = APP_PORT;

// 1. 错误处理中间件必须是第一个
app.use(jsonErrorHandler);

// 2. 其他全局中间件
// Logger
app.use(async (ctx, next) => {
	await next();
	const rt = ctx.response.headers.get("X-Response-Time");
	console.log(`${ctx.request.method} ${ctx.request.url} - ${rt}`);
});

// Response Time
app.use(async (ctx, next) => {
	const start = Date.now();
	await next();
	const ms = Date.now() - start;
	ctx.response.headers.set("X-Response-Time", `${ms}ms`);
});

// 3. 使用 useOakServer 附加所有控制器路由
useOakServer(app, [UserController, AuthController, ServiceAccountController, PingController]);

// 4. 启用 OpenAPI (Swagger UI)
useOas(app, {
	info: {
		title: "Inno-Mark-Server API",
		version: "1.0.0",
		description: "基于 Deno 和 DDD 的后端服务",
	},
	components: {
		securitySchemes: {
			bearerAuth: {
				type: "http",
				scheme: "bearer",
				bearerFormat: "JWT",
			},
		},
	},
	security: [
		{
			bearerAuth: [],
		},
	],
});

console.log(`Swagger UI available at http://localhost:${port}/swagger`);
console.log(`Server listening on http://localhost:${port}`);
await app.listen({ port });
