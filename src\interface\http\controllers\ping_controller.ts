import { Controller, Get } from "@dklab/oak-routing-ctrl";

@Controller("/api/v1")
export class PingController {
  
  @Get("/ping")
  public ping() {
    return {
      success: true,
      code: 20000,
      message: "pong",
      data: {
        timestamp: new Date().toISOString(),
      },
    };
  }

  @Get("/protected-ping", {
    summary: "受保护的 Ping 接口",
    description: "这是一个示例，未来需要 JWT 认证才能访问。",
  })
  public protectedPing() {
    return {
      success: true,
      code: 20000,
      message: "pong (protected)",
      data: {
        timestamp: new Date().toISOString(),
      },
    };
  }
} 