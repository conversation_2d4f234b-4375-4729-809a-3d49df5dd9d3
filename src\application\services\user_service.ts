// src/application/services/user_service.ts

import { IUserRepository } from "../../domain/repositories/user_repository.ts";
import { UserAggregate } from "../../domain/aggregates/user_aggregate.ts";
import { PhoneNumberVO } from "../../domain/value_objects/phone_number_vo.ts";
import { CreateUserDto, UserDto, UpdateUserPhoneNumberDto } from "../dtos/user_dtos.ts";
import { BusinessError, BusinessCode } from "../../domain/errors/business_error.ts";
import { hash } from "https://deno.land/x/bcrypt@v0.4.1/mod.ts";
import { RegisterUserDTO, UserResponseDTO } from "../dtos/user_dto.ts";

/**
 * @description 用户应用服务
 */
export class UserService {
  constructor(private readonly userRepository: IUserRepository) {}

  /**
   * @description 注册新用户
   * @param createUserDto 创建用户 DTO
   * @returns Promise<UserDto> 创建成功后的用户信息 DTO
   * @throws BusinessError 如果手机号已存在或格式无效
   */
  async registerUser(createUserDto: CreateUserDto): Promise<UserDto> {
    const phoneNumberVO = PhoneNumberVO.create(createUserDto.phoneNumber);

    // 检查手机号是否已注册
    const existingUser = await this.userRepository.findByPhoneNumber(phoneNumberVO);
    if (existingUser) {
      throw new BusinessError(BusinessCode.EMAIL_ALREADY_EXISTS, "该手机号码已被注册。"); // 复用，或创建 PHONE_NUMBER_ALREADY_EXISTS
    }

    // 生成新用户ID
    const newUserId = await this.userRepository.nextId();

    // 创建用户聚合
    const newUser = UserAggregate.createNew(newUserId, phoneNumberVO);

    // 持久化用户
    await this.userRepository.save(newUser);

    // 转换为 DTO 返回
    return this.mapToUserDto(newUser);
  }

  /**
   * @description 根据用户ID获取用户信息
   * @param userId 用户ID
   * @returns Promise<UserDto | null> 用户信息DTO或null
   */
  async getUserById(userId: string): Promise<UserDto | null> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      return null;
    }
    return this.mapToUserDto(user);
  }

  /**
   * @description 根据手机号码获取用户信息
   * @param phoneNumber 手机号码字符串
   * @returns Promise<UserDto | null> 用户信息DTO或null
   * @throws BusinessError 如果手机号码格式无效
   */
  async getUserByPhoneNumber(phoneNumber: string): Promise<UserDto | null> {
    const phoneNumberVO = PhoneNumberVO.create(phoneNumber);
    const user = await this.userRepository.findByPhoneNumber(phoneNumberVO);
    if (!user) {
      return null;
    }
    return this.mapToUserDto(user);
  }

  /**
   * @description 更新用户手机号码
   * @param userId 要更新的用户的ID
   * @param updateUserPhoneNumberDto 更新手机号的DTO
   * @returns Promise<UserDto>
   * @throws BusinessError 如果用户未找到，或新手机号格式无效/已被占用
   */
  async updateUserPhoneNumber(
    userId: string,
    updateDto: UpdateUserPhoneNumberDto,
  ): Promise<UserDto> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessError(BusinessCode.USER_NOT_FOUND, "用户不存在。");
    }

    const newPhoneNumberVO = PhoneNumberVO.create(updateDto.newPhoneNumber);

    // 检查新手机号是否已被其他用户占用
    if (!user.phoneNumber.equals(newPhoneNumberVO)) { // 仅当手机号实际改变时才检查
        const existingUserWithNewPhone = await this.userRepository.findByPhoneNumber(newPhoneNumberVO);
        if (existingUserWithNewPhone && existingUserWithNewPhone.id !== userId) {
            throw new BusinessError(BusinessCode.EMAIL_ALREADY_EXISTS, "该新手机号码已被其他用户注册。");
        }
    }

    user.changePhoneNumber(newPhoneNumberVO);
    await this.userRepository.save(user);

    return this.mapToUserDto(user);
  }

  /**
   * @description 删除用户
   * @param userId 用户ID
   * @returns Promise<void>
   * @throws BusinessError 如果用户未找到
   */
  async deleteUser(userId: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessError(BusinessCode.USER_NOT_FOUND, "无法删除：用户不存在。");
    }
    await this.userRepository.delete(userId);
  }

  /**
   * @description 将用户聚合映射到用户信息 DTO
   * @param user 用户聚合实例
   * @returns UserDto
   */
  private mapToUserDto(user: UserAggregate): UserDto {
    return {
      id: user.id,
      phoneNumber: user.phoneNumber.value,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };
  }

  /**
   * @description 用户注册
   * @param dto 注册数据传输对象
   * @returns Promise<UserResponseDTO>
   */
  async register(dto: RegisterUserDTO): Promise<UserResponseDTO> {
    const phoneNumber = PhoneNumberVO.create(dto.phoneNumber);

    // 1. 检查手机号是否已存在
    const existingUser = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (existingUser) {
      throw new BusinessError(BusinessCode.PHONE_NUMBER_ALREADY_EXISTS, "该手机号码已被注册。");
    }

    // 2. 哈希密码 (如果提供了密码)
    let passwordHash: string | undefined = undefined;
    if (dto.password) {
      passwordHash = await hash(dto.password);
    }

    // 3. 创建用户聚合
    const newUserId = await this.userRepository.nextId();
    const newUser = UserAggregate.createNew(
      newUserId,
      phoneNumber,
      passwordHash,
      dto.wechatUnionId
    );

    // 4. 保存用户
    await this.userRepository.save(newUser);

    // 5. 返回 DTO
    return UserResponseDTO.fromDomain(newUser);
  }
}