// src/domain/entities/service_account_entity.ts

/**
 * @description 服务账户实体接口
 */
export interface IServiceAccountEntity {
  id: string; // 通常使用 UUID
  account: string; // 账号
  token: string; // 登录凭证
  expiryTime?: Date; // 过期时间，可选
  serviceCount: number; // 调用次数，0~10
  serviceTime?: Date; // 调用的时间，可选
  createdAt: Date;
  updatedAt: Date;
}

/**
 * @description 服务账户实体类
 */
export class ServiceAccountEntity implements IServiceAccountEntity {
  public readonly id: string;
  public account: string;
  public token: string;
  public expiryTime?: Date;
  public serviceCount: number;
  public serviceTime?: Date;
  public readonly createdAt: Date;
  public updatedAt: Date;

  protected constructor(
    id: string,
    account: string,
    token: string,
    serviceCount: number = 0,
    expiryTime?: Date,
    serviceTime?: Date,
    createdAt?: Date,
    updatedAt?: Date
  ) {
    this.id = id;
    this.account = account;
    this.token = token;
    this.serviceCount = serviceCount;
    this.expiryTime = expiryTime;
    this.serviceTime = serviceTime;
    this.createdAt = createdAt || new Date();
    this.updatedAt = updatedAt || new Date();
  }

  /**
   * @description 创建一个新的服务账户实体
   * @param id 服务账户ID (建议使用 UUID)
   * @param account 账号
   * @param token 登录凭证
   * @param expiryTime 过期时间
   * @param serviceCount 调用次数，默认为0
   * @param serviceTime 调用时间
   * @returns ServiceAccountEntity 实例
   */
  public static create(
    id: string,
    account: string,
    token: string,
    expiryTime?: Date,
    serviceCount: number = 0,
    serviceTime?: Date
  ): ServiceAccountEntity {
    // 验证调用次数范围
    if (serviceCount < 0 || serviceCount > 10) {
      throw new Error('Service count must be between 0 and 10');
    }
    
    return new ServiceAccountEntity(id, account, token, serviceCount, expiryTime, serviceTime);
  }

  /**
   * @description 从数据库数据重建服务账户实体
   * @param data 数据库数据
   * @returns ServiceAccountEntity 实例
   */
  public static fromData(data: {
    id: string;
    account: string;
    token: string;
    expiry_time?: Date;
    service_count: number;
    service_time?: Date;
    created_at: Date;
    updated_at: Date;
  }): ServiceAccountEntity {
    return new ServiceAccountEntity(
      data.id,
      data.account,
      data.token,
      data.service_count,
      data.expiry_time,
      data.service_time,
      data.created_at,
      data.updated_at
    );
  }

  /**
   * @description 更新登录凭证
   * @param newToken 新的登录凭证
   * @param newExpiryTime 新的过期时间
   */
  public updateToken(newToken: string, newExpiryTime?: Date): void {
    this.token = newToken;
    this.expiryTime = newExpiryTime;
    this.updatedAt = new Date();
  }

  /**
   * @description 增加服务调用次数
   * @throws Error 如果调用次数超过限制
   */
  public incrementServiceCount(): void {
    if (this.serviceCount >= 10) {
      throw new Error('Service count limit reached (maximum 10)');
    }
    
    this.serviceCount += 1;
    this.serviceTime = new Date();
    this.updatedAt = new Date();
  }

  /**
   * @description 重置服务调用次数
   */
  public resetServiceCount(): void {
    this.serviceCount = 0;
    this.serviceTime = undefined;
    this.updatedAt = new Date();
  }

  /**
   * @description 检查token是否已过期
   * @returns boolean 是否已过期
   */
  public isTokenExpired(): boolean {
    if (!this.expiryTime) {
      return false; // 没有过期时间则认为不过期
    }
    
    return new Date() > this.expiryTime;
  }

  /**
   * @description 检查是否可以继续调用服务
   * @returns boolean 是否可以调用
   */
  public canCallService(): boolean {
    return !this.isTokenExpired() && this.serviceCount < 10;
  }

  /**
   * @description 获取剩余调用次数
   * @returns number 剩余次数
   */
  public getRemainingServiceCount(): number {
    return Math.max(0, 10 - this.serviceCount);
  }
}
