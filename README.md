# Inno-Mark 服务器 (Deno DDD 示例)

这是一个基于 Deno、TypeScript 和领域驱动设计 (DDD) 构建的 RESTful 后端系统示例。

## 项目特点

- **运行时**: Deno (最新稳定版)
- **数据库**: PostgreSQL
- **语言**: TypeScript (完全类型化)
- **架构**: 领域驱动设计 (DDD)
  - `Domain` 层: 实体、聚合根、值对象、领域服务、仓储接口
  - `Application` 层: 应用服务、DTOs
  - `Infrastructure` 层: 仓储实现 (deno-postgres)、数据库客户端
  - `Interface` 层: RESTful API 控制器 (Oak)、路由
- **依赖管理**: Deno 原生模块 + `import_map.json`
- **配置**: `src/config.ts` (通过环境变量)
- **测试**: 单元测试 (计划中)

## 项目结构

```
.
├── src
│   ├── application       # 应用层: 服务、DTOs
│   │   ├── dtos
│   │   └── services
│   ├── domain            # 领域层: 实体、聚合、值对象、仓储接口
│   │   ├── aggregates
│   │   ├── entities
│   │   ├── repositories
│   │   └── value_objects
│   ├── infrastructure    # 基础设施层: 数据库交互、外部服务客户端
│   │   ├── database
│   │   └── repositories
│   ├── interface         # 接口层: API 控制器、路由、中间件
│   │   ├── constants
│   │   ├── controllers
│   │   ├── middlewares
│   │   └── routes.ts
│   ├── config.ts         # 项目配置
│   └── main.ts           # 应用入口
├── tests                 # 测试目录 (单元测试、集成测试)
│   ├── integration
│   └── unit
├── .env.example          # 环境变量示例文件
├── .gitignore
├── deno.jsonc            # Deno 配置文件 (任务、lint、fmt)
├── import_map.json       # 导入映射
├── init_db.sql           # 数据库初始化脚本
└── README.md             # 本文档
```

## 环境准备

1.  **安装 Deno**: 请参照 [Deno 官方文档](https://deno.land/#installation)进行安装。
2.  **安装 PostgreSQL**: 请确保本地或 Docker 中有可用的 PostgreSQL 服务。
3.  **配置环境变量**: 
    复制 `.env.example` 为 `.env` 文件，并根据您的 PostgreSQL 配置修改其中的变量。这些环境变量将通过 `deno run` 命令的 `--env=.env` 标志加载到应用中。
    示例 `.env` 文件内容：
    ```env
    APP_PORT=8000
    POSTGRES_HOST=localhost
    POSTGRES_PORT=5432
    POSTGRES_USER=your_postgres_user
    POSTGRES_PASSWORD=your_postgres_password
    POSTGRES_DB=inno_mark_db
    POSTGRES_POOL_SIZE=10
    ```

## 数据库初始化

在项目根目录下运行以下命令来初始化数据库表结构：

```bash
deno task db:init
```

如果您需要清空并重新初始化用户表，可以运行：

```bash
deno task db:reset
```

**注意**: `db:init` 和 `db:reset` 任务依赖 `psql` 命令行工具。请确保它已安装并配置在您的系统 PATH 中。

## 运行开发环境

```bash
deno task dev
```

此命令会使用 `deno run --allow-net --allow-read --allow-env --env=.env src/main.ts` 来启动应用，自动加载 `.env` 文件中的环境变量。
服务将启动在 `http://localhost:8000` (或 `.env` 文件中 `APP_PORT` 指定的端口)。

## 运行测试

运行所有测试：
```bash
deno task test
```

运行单元测试：
```bash
deno task test:unit
```

运行集成测试：
```bash
deno task test:integration
```

(目前测试文件尚未完全实现)

## API 端点示例 (用户模块)

- **POST /users**: 注册新用户
  - 请求体 (JSON):
    ```json
    {
      "phoneNumber": "13800138000"
    }
    ```
- **GET /users/:id**: 获取用户信息
- **PUT /users/:id/phone-number**: 更新用户手机号
  - 请求体 (JSON):
    ```json
    {
      "newPhoneNumber": "13900139000"
    }
    ```
- **DELETE /users/:id**: 删除用户

## 后续计划

- [ ] 实现完整的单元测试和集成测试。
- [ ] 添加更健壮的输入校验 (如 Zod)。
- [ ] 实现依赖注入容器 (如 Awilix)。
- [ ] 完善错误处理和日志记录。
- [ ] 添加用户认证与授权 (如 JWT)。
- [ ] 考虑使用数据库迁移工具。