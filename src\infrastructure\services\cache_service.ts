/**
 * @description 一个简单的内存缓存服务，用于存储微信 session_key 等临时数据。
 * 在生产环境中，建议替换为 Redis 或其他持久化缓存方案。
 */
export class CacheService {
  private cache = new Map<string, { value: any; expiresAt: number }>();
  private cleanupInterval: number;

  constructor(cleanupIntervalMs = 60 * 1000) { // 每分钟清理一次过期缓存
    this.cleanupInterval = setInterval(() => this.cleanup(), cleanupIntervalMs);
  }

  /**
   * @description 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param ttlSeconds 生存时间 (秒)
   */
  public set(key: string, value: any, ttlSeconds: number): void {
    const expiresAt = Date.now() + ttlSeconds * 1000;
    this.cache.set(key, { value, expiresAt });
  }

  /**
   * @description 获取缓存
   * @param key 缓存键
   * @returns 缓存值或 null
   */
  public get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (entry && Date.now() < entry.expiresAt) {
      return entry.value as T;
    }
    // 如果条目已过期，则删除它
    if (entry) {
      this.cache.delete(key);
    }
    return null;
  }

  /**
   * @description 删除缓存
   * @param key 缓存键
   */
  public delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * @description 清理过期的缓存条目
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now >= entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * @description 停止清理计时器 (应用关闭时调用)
   */
  public stopCleanup(): void {
    clearInterval(this.cleanupInterval);
  }
} 