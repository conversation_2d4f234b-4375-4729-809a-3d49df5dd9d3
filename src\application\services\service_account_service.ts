// src/application/services/service_account_service.ts

import { v4 as uuidv4 } from "uuid";
import { ServiceAccountEntity } from "../../domain/entities/service_account_entity.ts";
import { IServiceAccountRepository } from "../../domain/repositories/service_account_repository.ts";
import { ThirdPartyApiService } from "../../infrastructure/services/third_party_api_service.ts";
import { SERVICE_ACCOUNT_PASSWORD } from "../../config.ts";
import { 
	AddServiceAccountDTO, 
	ServiceAccountResponseDTO, 
	ServiceAccountListResponseDTO 
} from "../dtos/service_account_dto.ts";

/**
 * @description 服务账户应用服务
 */
export class ServiceAccountService {
	constructor(
		private readonly serviceAccountRepository: IServiceAccountRepository,
		private readonly thirdPartyApiService: ThirdPartyApiService
	) {}

	/**
	 * @description 添加服务账户
	 * @param dto 添加服务账户DTO
	 * @returns Promise<ServiceAccountResponseDTO>
	 */
	async addServiceAccount(dto: AddServiceAccountDTO): Promise<ServiceAccountResponseDTO> {
		// 检查账号是否已存在
		const existingAccount = await this.serviceAccountRepository.findByAccount(dto.account);
		if (existingAccount) {
			throw new Error(`Service account with account '${dto.account}' already exists`);
		}

		// 调用第三方API获取token
		const token = await this.thirdPartyApiService.getImToken(dto.account, SERVICE_ACCOUNT_PASSWORD);

		// 计算过期时间（一个月后）
		const expiryTime = new Date();
		expiryTime.setMonth(expiryTime.getMonth() + 1);

		// 当前时间作为服务调用时间
		const serviceTime = new Date();

		// 创建服务账户实体
		const serviceAccount = ServiceAccountEntity.create(
			uuidv4(),
			dto.account,
			token,
			expiryTime,
			serviceTime,
			10 // 默认调用次数为10
		);

		// 保存到数据库
		const savedAccount = await this.serviceAccountRepository.save(serviceAccount);

		// 转换为响应DTO
		return this.toResponseDTO(savedAccount);
	}

	/**
	 * @description 根据ID获取服务账户
	 * @param id 服务账户ID
	 * @returns Promise<ServiceAccountResponseDTO | null>
	 */
	async getServiceAccountById(id: string): Promise<ServiceAccountResponseDTO | null> {
		const serviceAccount = await this.serviceAccountRepository.findById(id);
		if (!serviceAccount) {
			return null;
		}
		return this.toResponseDTO(serviceAccount);
	}

	/**
	 * @description 根据账号获取服务账户
	 * @param account 账号
	 * @returns Promise<ServiceAccountResponseDTO | null>
	 */
	async getServiceAccountByAccount(account: string): Promise<ServiceAccountResponseDTO | null> {
		const serviceAccount = await this.serviceAccountRepository.findByAccount(account);
		if (!serviceAccount) {
			return null;
		}
		return this.toResponseDTO(serviceAccount);
	}

	/**
	 * @description 获取所有服务账户
	 * @returns Promise<ServiceAccountListResponseDTO>
	 */
	async getAllServiceAccounts(): Promise<ServiceAccountListResponseDTO> {
		const accounts = await this.serviceAccountRepository.findAll();
		return {
			accounts: accounts.map(account => this.toResponseDTO(account)),
			total: accounts.length
		};
	}

	/**
	 * @description 刷新服务账户token
	 * @param id 服务账户ID
	 * @returns Promise<ServiceAccountResponseDTO>
	 */
	async refreshServiceAccountToken(id: string): Promise<ServiceAccountResponseDTO> {
		const serviceAccount = await this.serviceAccountRepository.findById(id);
		if (!serviceAccount) {
			throw new Error(`Service account with id '${id}' not found`);
		}

		// 调用第三方API获取新token
		const newToken = await this.thirdPartyApiService.getImToken(
			serviceAccount.account, 
			SERVICE_ACCOUNT_PASSWORD
		);

		// 计算新的过期时间（一个月后）
		const newExpiryTime = new Date();
		newExpiryTime.setMonth(newExpiryTime.getMonth() + 1);

		// 更新token和过期时间
		serviceAccount.updateToken(newToken, newExpiryTime);

		// 保存更新
		const updatedAccount = await this.serviceAccountRepository.update(serviceAccount);

		return this.toResponseDTO(updatedAccount);
	}

	/**
	 * @description 删除服务账户
	 * @param id 服务账户ID
	 * @returns Promise<boolean>
	 */
	async deleteServiceAccount(id: string): Promise<boolean> {
		const serviceAccount = await this.serviceAccountRepository.findById(id);
		if (!serviceAccount) {
			throw new Error(`Service account with id '${id}' not found`);
		}

		return await this.serviceAccountRepository.delete(id);
	}

	/**
	 * @description 将实体转换为响应DTO
	 * @param entity 服务账户实体
	 * @returns ServiceAccountResponseDTO
	 */
	private toResponseDTO(entity: ServiceAccountEntity): ServiceAccountResponseDTO {
		return {
			id: entity.id,
			account: entity.account,
			serviceCount: entity.serviceCount,
			serviceTime: entity.serviceTime.toISOString(),
			expiryTime: entity.expiryTime.toISOString(),
			createdAt: entity.createdAt.toISOString(),
			updatedAt: entity.updatedAt.toISOString(),
		};
	}
}
