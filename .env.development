# .env.example - 环境变量示例文件
# 将此文件复制为 .env 并根据您的环境修改值

# 应用配置
APP_PORT=8000

# PostgreSQL 数据库连接配置
# 请确保这些值与您的 PostgreSQL 服务器设置匹配
POSTGRES_HOST=************
POSTGRES_PORT=5432
POSTGRES_USER=postgres # 例如: postgres
POSTGRES_PASSWORD=postgres # 例如: mysecretpassword
POSTGRES_DB=inno-mark # 您希望应用连接的数据库名称
POSTGRES_POOL_SIZE=10 # 数据库连接池中的最大连接数
WECHAT_APP_ID=wxbd18831b9da2707f
WECHAT_APP_SECRET=3fc2d2dc9075e5e43fcf225a09e4275a

# 如果您的 PostgreSQL 服务器需要 SSL 连接，请取消注释并配置以下变量：
# POSTGRES_SSL_MODE=require # 'disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full'
# POSTGRES_SSL_CERT_PATH=/path/to/client-cert.pem
# POSTGRES_SSL_KEY_PATH=/path/to/client-key.pem
# POSTGRES_SSL_CA_PATH=/path/to/server-ca.pem

# JWT 配置 (如果将来实现认证功能)
JWT_SECRET=Yk5xcGs73wWfbZfeRQAt
JWT_EXPIRATION_TIME=60d # 例如: 1h, 30m, 7d

# 其他第三方服务或应用特定配置
# EXAMPLE_API_KEY=your_example_api_key