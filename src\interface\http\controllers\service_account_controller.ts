// src/interface/http/controllers/service_account_controller.ts

import { Controller, Post, Get, Delete, ControllerMethodArgs, Middleware } from "@dklab/oak-routing-ctrl";
import { container } from "../../../container.ts";
import { AddServiceAccountDTO } from "../../../application/dtos/service_account_dto.ts";
import { AddServiceAccountSchema, ServiceAccountApiResponseSchema, ServiceAccountListApiResponseSchema } from "../schemas/service_account_schemas.ts";
import { serviceAccountAuthMiddleware } from "../middlewares/service_account_auth_middleware.ts";

// OpenAPI 规范定义
const addServiceAccountSpec = {
	summary: "添加服务账户",
	description: "添加一个新的第三方服务账户，需要Authorization验证",
	security: [{ sessionAuth: [] }],
	request: {
		body: {
			content: { "application/json": { schema: AddServiceAccountSchema } },
		},
	},
	responses: {
		"201": {
			description: "服务账户添加成功",
			content: {
				"application/json": {
					schema: ServiceAccountApiResponseSchema,
				},
			},
		},
		"400": {
			description: "请求参数错误",
		},
		"401": {
			description: "未授权访问",
		},
		"409": {
			description: "账号已存在",
		},
	},
};

const getServiceAccountsSpec = {
	summary: "获取所有服务账户",
	description: "获取所有服务账户列表，需要Authorization验证",
	security: [{ sessionAuth: [] }],
	responses: {
		"200": {
			description: "获取成功",
			content: {
				"application/json": {
					schema: ServiceAccountListApiResponseSchema,
				},
			},
		},
		"401": {
			description: "未授权访问",
		},
	},
};

const getServiceAccountSpec = {
	summary: "根据ID获取服务账户",
	description: "根据ID获取单个服务账户信息，需要Authorization验证",
	security: [{ sessionAuth: [] }],
	parameters: [
		{
			name: "id",
			in: "path",
			required: true,
			schema: { type: "string", format: "uuid" },
			description: "服务账户ID",
		},
	],
	responses: {
		"200": {
			description: "获取成功",
			content: {
				"application/json": {
					schema: ServiceAccountApiResponseSchema,
				},
			},
		},
		"404": {
			description: "服务账户不存在",
		},
		"401": {
			description: "未授权访问",
		},
	},
};

const refreshTokenSpec = {
	summary: "刷新服务账户token",
	description: "刷新指定服务账户的token，需要Authorization验证",
	security: [{ sessionAuth: [] }],
	parameters: [
		{
			name: "id",
			in: "path",
			required: true,
			schema: { type: "string", format: "uuid" },
			description: "服务账户ID",
		},
	],
	responses: {
		"200": {
			description: "刷新成功",
			content: {
				"application/json": {
					schema: ServiceAccountApiResponseSchema,
				},
			},
		},
		"404": {
			description: "服务账户不存在",
		},
		"401": {
			description: "未授权访问",
		},
	},
};

const deleteServiceAccountSpec = {
	summary: "删除服务账户",
	description: "删除指定的服务账户，需要Authorization验证",
	security: [{ sessionAuth: [] }],
	parameters: [
		{
			name: "id",
			in: "path",
			required: true,
			schema: { type: "string", format: "uuid" },
			description: "服务账户ID",
		},
	],
	responses: {
		"200": {
			description: "删除成功",
		},
		"404": {
			description: "服务账户不存在",
		},
		"401": {
			description: "未授权访问",
		},
	},
};

@Controller("/api/v1/service-accounts")
export class ServiceAccountController {
	private serviceAccountService = container.serviceAccountService;

	/**
	 * @description 添加服务账户
	 */
	@Post("/", addServiceAccountSpec)
	@Middleware(serviceAccountAuthMiddleware)
	@ControllerMethodArgs("body")
	public async addServiceAccount(dto: AddServiceAccountDTO) {
		try {
			const validatedDto = AddServiceAccountSchema.parse(dto);
			const result = await this.serviceAccountService.addServiceAccount(validatedDto);
			
			return {
				success: true,
				code: 20100,
				message: "服务账户添加成功",
				data: result,
			};
		} catch (error) {
			if (error.message.includes("already exists")) {
				return {
					success: false,
					code: 40900,
					message: error.message,
					data: null,
				};
			}
			throw error;
		}
	}

	/**
	 * @description 获取所有服务账户
	 */
	@Get("/", getServiceAccountsSpec)
	@Middleware(serviceAccountAuthMiddleware)
	public async getServiceAccounts() {
		try {
			const result = await this.serviceAccountService.getAllServiceAccounts();
			
			return {
				success: true,
				code: 20000,
				message: "获取服务账户列表成功",
				data: result,
			};
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @description 根据ID获取服务账户
	 */
	@Get("/:id", getServiceAccountSpec)
	@Middleware(serviceAccountAuthMiddleware)
	@ControllerMethodArgs("params")
	public async getServiceAccount(params: { id: string }) {
		try {
			const result = await this.serviceAccountService.getServiceAccountById(params.id);
			
			if (!result) {
				return {
					success: false,
					code: 40400,
					message: "服务账户不存在",
					data: null,
				};
			}
			
			return {
				success: true,
				code: 20000,
				message: "获取服务账户成功",
				data: result,
			};
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @description 刷新服务账户token
	 */
	@Post("/:id/refresh-token", refreshTokenSpec)
	@Middleware(serviceAccountAuthMiddleware)
	@ControllerMethodArgs("params")
	public async refreshToken(params: { id: string }) {
		try {
			const result = await this.serviceAccountService.refreshServiceAccountToken(params.id);
			
			return {
				success: true,
				code: 20000,
				message: "Token刷新成功",
				data: result,
			};
		} catch (error) {
			if (error.message.includes("not found")) {
				return {
					success: false,
					code: 40400,
					message: error.message,
					data: null,
				};
			}
			throw error;
		}
	}

	/**
	 * @description 删除服务账户
	 */
	@Delete("/:id", deleteServiceAccountSpec)
	@Middleware(serviceAccountAuthMiddleware)
	@ControllerMethodArgs("params")
	public async deleteServiceAccount(params: { id: string }) {
		try {
			const success = await this.serviceAccountService.deleteServiceAccount(params.id);
			
			return {
				success: true,
				code: 20000,
				message: "服务账户删除成功",
				data: { deleted: success },
			};
		} catch (error) {
			if (error.message.includes("not found")) {
				return {
					success: false,
					code: 40400,
					message: error.message,
					data: null,
				};
			}
			throw error;
		}
	}
}
