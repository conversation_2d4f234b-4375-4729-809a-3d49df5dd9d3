// src/domain/repositories/user_repository.ts

import { UserAggregate } from "../aggregates/user_aggregate.ts";
import { PhoneNumberVO } from "../value_objects/phone_number_vo.ts";

/**
 * @description 用户仓储接口，定义用户聚合的数据持久化操作
 */
export interface IUserRepository {
  /**
   * @description 根据手机号码查找用户聚合
   * @param phoneNumber 手机号码值对象
   * @returns Promise<UserAggregate | null> 用户聚合实例或 null (如果未找到)
   */
  findByPhoneNumber(phoneNumber: PhoneNumberVO): Promise<UserAggregate | null>;

  /**
   * @description 根据用户ID查找用户聚合
   * @param id 用户ID
   * @returns Promise<UserAggregate | null> 用户聚合实例或 null (如果未找到)
   */
  findById(id: string): Promise<UserAggregate | null>;

  /**
   * @description 根据微信 OpenID 或 UnionID 查找用户聚合
   * @param wechatId 微信 OpenID 或 UnionID
   * @returns Promise<UserAggregate | null> 用户聚合实例或 null (如果未找到)
   */
  findByWechatId(wechatId: string): Promise<UserAggregate | null>;

  /**
   * @description 保存用户聚合（创建或更新）
   * @param user 用户聚合实例
   * @returns Promise<void>
   */
  save(user: UserAggregate): Promise<void>;

  /**
   * @description 删除用户聚合
   * @param id 用户ID
   * @returns Promise<void>
   */
  delete(id: string): Promise<void>;

  /**
   * @description 生成下一个可用的用户ID (通常由数据库序列或UUID生成器实现)
   * @returns Promise<string>
   */
  nextId(): Promise<string>;
}

// 仓储接口的唯一标识符，用于依赖注入
export const IUserRepository = Symbol.for("IUserRepository");