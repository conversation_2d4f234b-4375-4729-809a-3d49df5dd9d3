import { UserService } from "./application/services/user_service.ts";
import { AuthService } from "./application/services/auth_service.ts";
import { PostgresUserRepository } from "./infrastructure/repositories/postgres_user_repository.ts";
import { IUserRepository } from "./domain/repositories/user_repository.ts";
import { WeChatService } from "./infrastructure/services/wechat_service.ts";
import { JwtService } from "./infrastructure/services/jwt_service.ts";
import { CacheService } from "./infrastructure/services/cache_service.ts";

// --- 仓储实例化 ---
const userRepo: IUserRepository = new PostgresUserRepository();

// --- 基础设施服务实例化 ---
const wechatService = new WeChatService();
const jwtService = new JwtService();
const cacheService = new CacheService();

// --- 应用服务实例化 ---
const userService = new UserService(userRepo);
const authService = new AuthService(userRepo, wechatService, jwtService, cacheService);


// --- 导出所有需要的实例 ---
export const container = {
  userService,
  authService,
  // 如果有其他服务或仓储，也在这里导出
}; 