import { UserService } from "./application/services/user_service.ts";
import { AuthService } from "./application/services/auth_service.ts";
import { ServiceAccountService } from "./application/services/service_account_service.ts";
import { PostgresUserRepository } from "./infrastructure/repositories/postgres_user_repository.ts";
import { PostgresServiceAccountRepository } from "./infrastructure/repositories/postgres_service_account_repository.ts";
import { IUserRepository } from "./domain/repositories/user_repository.ts";
import { IServiceAccountRepository } from "./domain/repositories/service_account_repository.ts";
import { WeChatService } from "./infrastructure/services/wechat_service.ts";
import { JwtService } from "./infrastructure/services/jwt_service.ts";
import { CacheService } from "./infrastructure/services/cache_service.ts";
import { ThirdPartyApiService } from "./infrastructure/services/third_party_api_service.ts";

// --- 仓储实例化 ---
const userRepo: IUserRepository = new PostgresUserRepository();
const serviceAccountRepo: IServiceAccountRepository = new PostgresServiceAccountRepository();

// --- 基础设施服务实例化 ---
const wechatService = new WeChatService();
const jwtService = new JwtService();
const cacheService = new CacheService();
const thirdPartyApiService = new ThirdPartyApiService();

// --- 应用服务实例化 ---
const userService = new UserService(userRepo);
const authService = new AuthService(userRepo, wechatService, jwtService);
const serviceAccountService = new ServiceAccountService(serviceAccountRepo, thirdPartyApiService);

// --- 导出所有需要的实例 ---
export const container = {
	userService,
	authService,
	serviceAccountService,
	// 如果有其他服务或仓储，也在这里导出
};
