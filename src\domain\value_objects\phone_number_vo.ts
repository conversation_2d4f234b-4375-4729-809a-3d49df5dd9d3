// src/domain/value_objects/phone_number_vo.ts

import { BusinessError, BusinessCode } from "../../domain/errors/business_error.ts";

/**
 * @description 手机号码值对象
 */
export class PhoneNumberVO {
  private readonly _value: string;

  private constructor(value: string) {
    this._value = value;
  }

  /**
   * @description 获取手机号码字符串
   */
  public get value(): string {
    return this._value;
  }

  /**
   * @description 创建 PhoneNumberVO 实例
   * @param phoneNumber 手机号码字符串
   * @returns PhoneNumberVO 实例
   * @throws BusinessError 如果手机号码格式无效
   */
  public static create(phoneNumber: string): PhoneNumberVO {
    if (!this.isValid(phoneNumber)) {
      throw new BusinessError(
        BusinessCode.VALIDATION_ERROR,
        `无效的手机号码格式: ${phoneNumber}`,
      );
    }
    return new PhoneNumberVO(phoneNumber);
  }

  /**
   * @description 验证手机号码格式是否有效 (简单示例，实际应使用更完善的正则)
   * @param phoneNumber 手机号码字符串
   * @returns boolean 是否有效
   */
  private static isValid(phoneNumber: string): boolean {
    // 简单校验：11位数字，以1开头
    // TODO: 根据实际需求替换为更严格的手机号校验正则表达式
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * @description 判断两个 PhoneNumberVO 是否相等
   * @param other 另一个 PhoneNumberVO 实例
   * @returns boolean 是否相等
   */
  public equals(other?: PhoneNumberVO): boolean {
    if (other === null || other === undefined) {
      return false;
    }
    if (!(other instanceof PhoneNumberVO)) {
      return false;
    }
    return this._value === other._value;
  }
}