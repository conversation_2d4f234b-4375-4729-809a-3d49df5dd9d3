import { container } from "../../../container.ts";
import { RegisterUserDTO } from "../../../application/dtos/user_dto.ts";
import { Controller, Post, ControllerMethodArgs, Get, z } from "@dklab/oak-routing-ctrl";
import { RegisterUserSchema, UserResponseSchema } from "../schemas/user_schemas.ts";

const registerUserSpec = {
  summary: "用户注册",
  description: "通过手机号和可选的密码、微信ID进行注册。",
  request: {
    body: {
      content: { "application/json": { schema: RegisterUserSchema } },
    },
  },
  responses: {
    "201": {
      description: "用户注册成功",
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            code: z.literal(20100),
            message: z.string(),
            data: UserResponseSchema,
          }),
        },
      },
    },
  },
};

@Controller("/api/v1/users")
export class UserController {
  private userService = container.userService;

  /**
   * @description 注册新用户
   */
  @Post("/register", registerUserSpec)
  @ControllerMethodArgs("body")
  public async register(dto: RegisterUserDTO) {
    const validatedDto = RegisterUserSchema.parse(dto);
    const newUser = await this.userService.register(validatedDto);

    return {
      success: true,
      code: 20100,
      message: "用户注册成功",
      data: newUser,
    };
  }
} 