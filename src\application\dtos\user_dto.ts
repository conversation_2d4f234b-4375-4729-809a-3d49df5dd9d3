import { UserAggregate } from "../../domain/aggregates/user_aggregate.ts";

/**
 * @description 用户注册时客户端传入的数据
 */
export class RegisterUserDTO {
  phoneNumber!: string;
  password?: string;
  wechatUnionId?: string;
}

/**
 * @description 返回给客户端的用户信息
 */
export class UserResponseDTO {
  id!: string;
  phoneNumber!: string;
  wechatUnionId?: string;
  createdAt!: Date;
  updatedAt!: Date;

  public static fromDomain(user: UserAggregate): UserResponseDTO {
    const dto = new UserResponseDTO();
    dto.id = user.id;
    dto.phoneNumber = user.phoneNumber.value;
    dto.wechatUnionId = user.wechatUnionId;
    dto.createdAt = user.createdAt;
    dto.updatedAt = user.updatedAt;
    return dto;
  }
} 